<?php
/**
 * InvoiceData Model
 * MTC Invoice Management System
 */

class InvoiceData extends BaseModel {
    protected $table = 'invoice_data';
    protected $fillable = [
        'firebase_id', 'description', 'location', 'qr', 'lpo', 'inb', 
        'amount', 'w_a', 'payment_status', 'timestamp', 'created_by'
    ];
    
    /**
     * Get invoice data with pagination and search
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getInvoiceData($filters = [], $page = 1, $limit = 25) {
        $offset = ($page - 1) * $limit;
        $conditions = [];
        $params = [];
        $sql = "SELECT * FROM {$this->table}";
        
        // Build WHERE clause based on filters
        if (!empty($filters['search'])) {
            $searchFields = ['description', 'location', 'qr', 'lpo', 'inb'];
            $searchConditions = [];
            foreach ($searchFields as $field) {
                $searchConditions[] = "{$field} LIKE ?";
                $params[] = "%{$filters['search']}%";
            }
            $conditions[] = "(" . implode(' OR ', $searchConditions) . ")";
        }
        
        if (!empty($filters['payment_status'])) {
            $conditions[] = "payment_status = ?";
            $params[] = $filters['payment_status'];
        }
        
        if (!empty($filters['location'])) {
            $conditions[] = "location LIKE ?";
            $params[] = "%{$filters['location']}%";
        }
        
        if (!empty($filters['date_from'])) {
            $conditions[] = "timestamp >= ?";
            $params[] = strtotime($filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $conditions[] = "timestamp <= ?";
            $params[] = strtotime($filters['date_to'] . ' 23:59:59');
        }
        
        if (!empty($filters['amount_min'])) {
            $conditions[] = "amount >= ?";
            $params[] = $filters['amount_min'];
        }
        
        if (!empty($filters['amount_max'])) {
            $conditions[] = "amount <= ?";
            $params[] = $filters['amount_max'];
        }
        
        // Add WHERE clause if conditions exist
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        // Count total records for pagination
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as filtered_results";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = (int) $totalResult['total'];
        
        // Add ORDER BY and LIMIT
        $sql .= " ORDER BY timestamp DESC LIMIT {$limit} OFFSET {$offset}";
        
        $results = $this->db->fetchAll($sql, $params);
        
        return [
            'data' => $results,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ];
    }
    
    /**
     * Get dashboard statistics
     * @return array
     */
    public function getDashboardStats() {
        $stats = [];
        
        // Total invoices
        $stats['total_invoices'] = $this->count();
        
        // Total amount
        $result = $this->db->fetchOne("SELECT SUM(amount) as total_amount FROM {$this->table}");
        $stats['total_amount'] = (float) $result['total_amount'] ?? 0;
        
        // Payment status breakdown
        $statusResult = $this->db->fetchAll("
            SELECT payment_status, COUNT(*) as count, SUM(amount) as amount 
            FROM {$this->table} 
            GROUP BY payment_status
        ");
        
        $stats['by_status'] = [];
        foreach ($statusResult as $row) {
            $stats['by_status'][$row['payment_status']] = [
                'count' => (int) $row['count'],
                'amount' => (float) $row['amount']
            ];
        }
        
        // Recent activity (last 30 days)
        $thirtyDaysAgo = time() - (30 * 24 * 60 * 60);
        $stats['recent_invoices'] = $this->count(['timestamp >=' => $thirtyDaysAgo]);
        
        // Monthly trend (last 12 months)
        $monthlyResult = $this->db->fetchAll("
            SELECT 
                DATE_FORMAT(FROM_UNIXTIME(timestamp), '%Y-%m') as month,
                COUNT(*) as count,
                SUM(amount) as amount
            FROM {$this->table} 
            WHERE timestamp >= ?
            GROUP BY DATE_FORMAT(FROM_UNIXTIME(timestamp), '%Y-%m')
            ORDER BY month DESC
            LIMIT 12
        ", [time() - (365 * 24 * 60 * 60)]);
        
        $stats['monthly_trend'] = array_reverse($monthlyResult);
        
        return $stats;
    }
    
    /**
     * Create invoice data with validation
     * @param array $data
     * @return int|false
     */
    public function createInvoiceData($data) {
        // Validate required fields
        $required = ['description', 'location', 'qr', 'lpo', 'inb', 'amount', 'w_a', 'payment_status'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new InvalidArgumentException("Field '{$field}' is required");
            }
        }
        
        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] < 0) {
            throw new InvalidArgumentException("Amount must be a positive number");
        }
        
        // Validate payment status
        $validStatuses = ['paid', 'pending', 'overdue', 'cancelled'];
        if (!in_array($data['payment_status'], $validStatuses)) {
            throw new InvalidArgumentException("Invalid payment status");
        }
        
        // Set timestamp if not provided
        if (empty($data['timestamp'])) {
            $data['timestamp'] = time() * 1000; // Milliseconds to match Firebase
        }
        
        return $this->create($data);
    }
    
    /**
     * Update invoice data with validation
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateInvoiceData($id, $data) {
        // Remove timestamp from updates (shouldn't be changed)
        unset($data['timestamp']);
        
        // Validate amount if provided
        if (isset($data['amount']) && (!is_numeric($data['amount']) || $data['amount'] < 0)) {
            throw new InvalidArgumentException("Amount must be a positive number");
        }
        
        // Validate payment status if provided
        if (isset($data['payment_status'])) {
            $validStatuses = ['paid', 'pending', 'overdue', 'cancelled'];
            if (!in_array($data['payment_status'], $validStatuses)) {
                throw new InvalidArgumentException("Invalid payment status");
            }
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * Get invoice data by Firebase ID (for migration)
     * @param string $firebaseId
     * @return array|false
     */
    public function getByFirebaseId($firebaseId) {
        $sql = "SELECT * FROM {$this->table} WHERE firebase_id = ?";
        return $this->db->fetchOne($sql, [$firebaseId]);
    }
    
    /**
     * Bulk import from Firebase data
     * @param array $firebaseData
     * @return array
     */
    public function bulkImportFromFirebase($firebaseData) {
        $imported = 0;
        $errors = [];
        
        $this->beginTransaction();
        
        try {
            foreach ($firebaseData as $firebaseId => $data) {
                // Check if already imported
                if ($this->getByFirebaseId($firebaseId)) {
                    continue;
                }
                
                // Prepare data for import
                $importData = [
                    'firebase_id' => $firebaseId,
                    'description' => $data['description'] ?? '',
                    'location' => $data['location'] ?? '',
                    'qr' => $data['qr'] ?? '',
                    'lpo' => $data['lpo'] ?? '',
                    'inb' => $data['inb'] ?? '',
                    'amount' => $data['amount'] ?? 0,
                    'w_a' => $data['w_a'] ?? '',
                    'payment_status' => $data['paymentStatus'] ?? 'pending',
                    'timestamp' => $data['timestamp'] ?? time() * 1000
                ];
                
                if ($this->create($importData)) {
                    $imported++;
                } else {
                    $errors[] = "Failed to import Firebase ID: {$firebaseId}";
                }
            }
            
            $this->commit();
            
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
        
        return [
            'imported' => $imported,
            'errors' => $errors
        ];
    }
}

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/update_delete_iv_traker"
    android:padding="7dp">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieAnimationView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/loading" />

    <!-- Add the Search Bar -->
    <EditText
        android:id="@+id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_margin="7dp"
        android:background="@drawable/rounded_search_background"
        android:drawableStart="@drawable/ic_search"
        android:drawablePadding="8dp"
        android:hint="Search Invoice Track..."
        android:inputType="text"
        android:padding="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/search_bar"
        android:layout_marginTop="8dp" />

</RelativeLayout>

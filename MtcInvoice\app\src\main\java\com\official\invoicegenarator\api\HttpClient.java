package com.official.invoicegenarator.api;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;

/**
 * HTTP Client for API communication
 * MTC Invoice Management System
 */
public class HttpClient {
    
    private static final String TAG = "HttpClient";
    private static final String PREFS_NAME = "ApiPrefs";
    private static final String KEY_CSRF_TOKEN = "csrf_token";
    private static final String KEY_SESSION_ID = "session_id";
    
    private static HttpClient instance;
    private OkHttpClient client;
    private Context context;
    private SharedPreferences prefs;
    
    private HttpClient(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        initializeClient();
    }
    
    public static synchronized HttpClient getInstance(Context context) {
        if (instance == null) {
            instance = new HttpClient(context);
        }
        return instance;
    }
    
    private void initializeClient() {
        HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
        logging.setLevel(HttpLoggingInterceptor.Level.BODY);
        
        client = new OkHttpClient.Builder()
                .connectTimeout(ApiConfig.CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(ApiConfig.READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(ApiConfig.WRITE_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(logging)
                .build();
    }
    
    /**
     * Make a GET request
     */
    public void get(String endpoint, ApiCallback callback) {
        String url = ApiConfig.getFullUrl(endpoint);
        
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", ApiConfig.CONTENT_TYPE_JSON)
                .build();
        
        executeRequest(request, callback);
    }
    
    /**
     * Make a POST request with JSON data
     */
    public void post(String endpoint, JSONObject data, ApiCallback callback) {
        String url = ApiConfig.getFullUrl(endpoint);
        
        // Add CSRF token to data
        try {
            String csrfToken = getCSRFToken();
            if (csrfToken != null && !csrfToken.isEmpty()) {
                data.put("csrf_token", csrfToken);
            }
        } catch (JSONException e) {
            Log.e(TAG, "Error adding CSRF token", e);
        }
        
        RequestBody body = RequestBody.create(
                data.toString(),
                MediaType.parse(ApiConfig.CONTENT_TYPE_JSON)
        );
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", ApiConfig.CONTENT_TYPE_JSON);
        
        // Add CSRF token header
        String csrfToken = getCSRFToken();
        if (csrfToken != null && !csrfToken.isEmpty()) {
            requestBuilder.addHeader(ApiConfig.HEADER_CSRF_TOKEN, csrfToken);
        }
        
        Request request = requestBuilder.build();
        executeRequest(request, callback);
    }
    
    /**
     * Upload file with multipart form data
     */
    public void uploadFile(String endpoint, File file, JSONObject additionalData, ApiCallback callback) {
        String url = ApiConfig.getFullUrl(endpoint);
        
        // Check file validity
        if (!ApiConfig.isFileTypeAllowed(file.getName())) {
            callback.onError("File type not allowed");
            return;
        }
        
        if (!ApiConfig.isFileSizeValid(file.length())) {
            callback.onError("File size exceeds maximum limit");
            return;
        }
        
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);
        
        // Add file
        RequestBody fileBody = RequestBody.create(file, MediaType.parse("application/octet-stream"));
        builder.addFormDataPart("file", file.getName(), fileBody);
        
        // Add additional data
        if (additionalData != null) {
            additionalData.keys().forEachRemaining(key -> {
                try {
                    builder.addFormDataPart(key, additionalData.getString(key));
                } catch (JSONException e) {
                    Log.e(TAG, "Error adding form data", e);
                }
            });
        }
        
        // Add CSRF token
        String csrfToken = getCSRFToken();
        if (csrfToken != null && !csrfToken.isEmpty()) {
            builder.addFormDataPart("csrf_token", csrfToken);
        }
        
        RequestBody requestBody = builder.build();
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);
        
        // Add CSRF token header
        if (csrfToken != null && !csrfToken.isEmpty()) {
            requestBuilder.addHeader(ApiConfig.HEADER_CSRF_TOKEN, csrfToken);
        }
        
        Request request = requestBuilder.build();
        executeRequest(request, callback);
    }
    
    /**
     * Execute HTTP request
     */
    private void executeRequest(Request request, ApiCallback callback) {
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Request failed", e);
                callback.onError("Network error: " + e.getMessage());
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseBody = response.body() != null ? response.body().string() : "";
                
                try {
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    
                    if (response.isSuccessful()) {
                        // Update CSRF token if present in response
                        if (jsonResponse.has("csrf_token")) {
                            saveCSRFToken(jsonResponse.getString("csrf_token"));
                        }
                        
                        callback.onSuccess(jsonResponse);
                    } else {
                        String errorMessage = "Request failed";
                        if (jsonResponse.has("message")) {
                            errorMessage = jsonResponse.getString("message");
                        }
                        callback.onError(errorMessage);
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing response", e);
                    callback.onError("Invalid response format");
                }
            }
        });
    }
    
    /**
     * Get stored CSRF token
     */
    private String getCSRFToken() {
        return prefs.getString(KEY_CSRF_TOKEN, "");
    }
    
    /**
     * Save CSRF token
     */
    private void saveCSRFToken(String token) {
        prefs.edit().putString(KEY_CSRF_TOKEN, token).apply();
    }
    
    /**
     * Clear stored tokens
     */
    public void clearTokens() {
        prefs.edit()
                .remove(KEY_CSRF_TOKEN)
                .remove(KEY_SESSION_ID)
                .apply();
    }
    
    /**
     * Initialize CSRF token by making a request to get it
     */
    public void initializeCSRFToken(ApiCallback callback) {
        get("auth/check-session", new ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                try {
                    if (response.has("csrf_token")) {
                        saveCSRFToken(response.getString("csrf_token"));
                    }
                    callback.onSuccess(response);
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing CSRF token", e);
                    callback.onError("Failed to initialize CSRF token");
                }
            }
            
            @Override
            public void onError(String error) {
                // Even if session check fails, we might get CSRF token
                callback.onError(error);
            }
        });
    }
    
    /**
     * API Callback interface
     */
    public interface ApiCallback {
        void onSuccess(JSONObject response);
        void onError(String error);
    }
}

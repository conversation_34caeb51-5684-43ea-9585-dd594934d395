<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.official.invoicegenarator"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Need this for API 33 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <permission
        android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.NewInvoice" >
        <activity
            android:name="com.official.invoicegenarator.InvoiceTwo"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.InvoiceTraker"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.PdfViewerActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.DownloadListActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.MoneyBagActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.FingerprintSettingsActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.VarifyActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.WorkerAttendenceActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.SelectionActivity"
            android:exported="false" />
        <activity
            android:name="com.official.invoicegenarator.Home"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.official.invoicegenarator.MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.karumi.dexter.DexterActivity"
            android:theme="@style/Dexter.Internal.Theme.Transparent" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.official.invoicegenarator.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
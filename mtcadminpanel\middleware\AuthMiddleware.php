<?php
/**
 * Authentication Middleware
 * MTC Invoice Management System
 */

class AuthMiddleware {
    
    /**
     * Check if user is authenticated
     * @return bool
     */
    public static function isAuthenticated() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Require authentication - redirect if not authenticated
     * @param string $redirectUrl
     */
    public static function requireAuth($redirectUrl = 'login.php') {
        if (!self::isAuthenticated()) {
            if (self::isAjaxRequest()) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Authentication required']);
                exit;
            } else {
                header("Location: $redirectUrl");
                exit;
            }
        }
        
        // Check session timeout
        if (self::isSessionExpired()) {
            self::destroySession();
            if (self::isAjaxRequest()) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Session expired']);
                exit;
            } else {
                header("Location: $redirectUrl?expired=1");
                exit;
            }
        }
    }
    
    /**
     * Require specific role
     * @param string|array $roles
     * @param string $redirectUrl
     */
    public static function requireRole($roles, $redirectUrl = 'dashboard.php') {
        self::requireAuth();
        
        if (is_string($roles)) {
            $roles = [$roles];
        }
        
        $userRole = $_SESSION['role'] ?? '';
        
        if (!in_array($userRole, $roles)) {
            if (self::isAjaxRequest()) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Insufficient permissions']);
                exit;
            } else {
                header("Location: $redirectUrl?error=insufficient_permissions");
                exit;
            }
        }
    }
    
    /**
     * Check if session is expired
     * @return bool
     */
    public static function isSessionExpired() {
        $loginTime = $_SESSION['login_time'] ?? 0;
        return (time() - $loginTime) > SESSION_TIMEOUT;
    }
    
    /**
     * Destroy session
     */
    public static function destroySession() {
        session_destroy();
        session_start();
        session_regenerate_id(true);
    }
    
    /**
     * Check if request is AJAX
     * @return bool
     */
    public static function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * Get current user ID
     * @return int|null
     */
    public static function getCurrentUserId() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current user role
     * @return string|null
     */
    public static function getCurrentUserRole() {
        return $_SESSION['role'] ?? null;
    }
    
    /**
     * Get current username
     * @return string|null
     */
    public static function getCurrentUsername() {
        return $_SESSION['username'] ?? null;
    }
    
    /**
     * Check if user has specific role
     * @param string|array $roles
     * @return bool
     */
    public static function hasRole($roles) {
        if (!self::isAuthenticated()) {
            return false;
        }
        
        if (is_string($roles)) {
            $roles = [$roles];
        }
        
        $userRole = $_SESSION['role'] ?? '';
        return in_array($userRole, $roles);
    }
    
    /**
     * Refresh session timeout
     */
    public static function refreshSession() {
        if (self::isAuthenticated()) {
            $_SESSION['login_time'] = time();
        }
    }
    
    /**
     * Generate and store CSRF token
     * @return string
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate CSRF token
     * @param string $token
     * @return bool
     */
    public static function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && 
               hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Require CSRF token validation
     */
    public static function requireCSRF() {
        $token = $_POST['csrf_token'] ?? 
                 $_GET['csrf_token'] ?? 
                 ($_SERVER['HTTP_X_CSRF_TOKEN'] ?? '');
        
        if (!self::validateCSRFToken($token)) {
            if (self::isAjaxRequest()) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
                exit;
            } else {
                header('Location: dashboard.php?error=csrf_invalid');
                exit;
            }
        }
    }
    
    /**
     * Log security event
     * @param string $event
     * @param array $details
     */
    public static function logSecurityEvent($event, $details = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'user_id' => self::getCurrentUserId(),
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details
        ];
        
        error_log("Security Event: " . json_encode($logData));
    }
    
    /**
     * Rate limiting check
     * @param string $key
     * @param int $maxAttempts
     * @param int $timeWindow
     * @return bool
     */
    public static function checkRateLimit($key, $maxAttempts = 5, $timeWindow = 300) {
        $sessionKey = "rate_limit_{$key}";
        $now = time();
        
        if (!isset($_SESSION[$sessionKey])) {
            $_SESSION[$sessionKey] = [];
        }
        
        // Clean old attempts
        $_SESSION[$sessionKey] = array_filter($_SESSION[$sessionKey], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        // Check if limit exceeded
        if (count($_SESSION[$sessionKey]) >= $maxAttempts) {
            return false;
        }
        
        // Add current attempt
        $_SESSION[$sessionKey][] = $now;
        
        return true;
    }
    
    /**
     * Block IP address temporarily
     * @param string $reason
     */
    public static function blockIP($reason = 'Security violation') {
        $ip = getClientIP();
        self::logSecurityEvent('IP_BLOCKED', ['ip' => $ip, 'reason' => $reason]);
        
        // In a production environment, you might want to store this in database
        // or use a more sophisticated blocking mechanism
        http_response_code(429);
        echo json_encode(['success' => false, 'message' => 'Access temporarily blocked']);
        exit;
    }
}

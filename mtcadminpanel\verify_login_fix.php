<?php
// Comprehensive login system verification
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Login System Verification</h2>";

$tests = [];
$allPassed = true;

// Test 1: Database Connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=invoice_masudvi;charset=utf8mb4', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $tests['database'] = ['status' => 'PASS', 'message' => 'Database connection successful'];
} catch (Exception $e) {
    $tests['database'] = ['status' => 'FAIL', 'message' => 'Database connection failed: ' . $e->getMessage()];
    $allPassed = false;
}

// Test 2: Admin User Exists
try {
    $stmt = $pdo->prepare('SELECT id, username, is_active, password_hash FROM users WHERE username = ?');
    $stmt->execute(['admin']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && $user['is_active']) {
        $tests['admin_user'] = ['status' => 'PASS', 'message' => 'Admin user exists and is active'];
    } else {
        $tests['admin_user'] = ['status' => 'FAIL', 'message' => 'Admin user not found or inactive'];
        $allPassed = false;
    }
} catch (Exception $e) {
    $tests['admin_user'] = ['status' => 'FAIL', 'message' => 'Error checking admin user: ' . $e->getMessage()];
    $allPassed = false;
}

// Test 3: Password Verification
if (isset($user)) {
    if (password_verify('admin123', $user['password_hash'])) {
        $tests['password'] = ['status' => 'PASS', 'message' => 'Password "admin123" is correct'];
    } else {
        $tests['password'] = ['status' => 'FAIL', 'message' => 'Password "admin123" is incorrect'];
        $allPassed = false;
    }
} else {
    $tests['password'] = ['status' => 'SKIP', 'message' => 'Skipped - admin user not found'];
}

// Test 4: CSRF Token Generation
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

if (isset($_SESSION['csrf_token']) && strlen($_SESSION['csrf_token']) > 0) {
    $tests['csrf_token'] = ['status' => 'PASS', 'message' => 'CSRF token generated successfully'];
} else {
    $tests['csrf_token'] = ['status' => 'FAIL', 'message' => 'CSRF token generation failed'];
    $allPassed = false;
}

// Test 5: Config Files
try {
    require_once 'config/config.php';
    $tests['config'] = ['status' => 'PASS', 'message' => 'Config files loaded successfully'];
} catch (Exception $e) {
    $tests['config'] = ['status' => 'FAIL', 'message' => 'Config loading failed: ' . $e->getMessage()];
    $allPassed = false;
}

// Test 6: User Model
try {
    require_once 'models/User.php';
    $userModel = new User();
    $authResult = $userModel->authenticate('admin', 'admin123');
    
    if ($authResult) {
        $tests['user_model'] = ['status' => 'PASS', 'message' => 'User model authentication works'];
    } else {
        $tests['user_model'] = ['status' => 'FAIL', 'message' => 'User model authentication failed'];
        $allPassed = false;
    }
} catch (Exception $e) {
    $tests['user_model'] = ['status' => 'FAIL', 'message' => 'User model error: ' . $e->getMessage()];
    $allPassed = false;
}

// Test 7: API Endpoint Accessibility
$apiUrl = 'http://192.168.0.106/MtcInvoiceMasudvi/mtcadminpanel/api/auth/login';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_NOBODY, true);
$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200 || $httpCode == 405) { // 405 is expected for GET request to POST endpoint
    $tests['api_endpoint'] = ['status' => 'PASS', 'message' => 'API endpoint is accessible'];
} else {
    $tests['api_endpoint'] = ['status' => 'FAIL', 'message' => "API endpoint returned HTTP $httpCode"];
    $allPassed = false;
}

// Display Results
echo "<div style='font-family: monospace;'>";
foreach ($tests as $testName => $result) {
    $icon = $result['status'] == 'PASS' ? '✅' : ($result['status'] == 'FAIL' ? '❌' : '⚠️');
    $color = $result['status'] == 'PASS' ? 'green' : ($result['status'] == 'FAIL' ? 'red' : 'orange');
    
    echo "<p style='color: $color;'>";
    echo "<strong>" . ucwords(str_replace('_', ' ', $testName)) . ":</strong> ";
    echo "$icon {$result['status']} - {$result['message']}";
    echo "</p>";
}
echo "</div>";

echo "<hr>";

if ($allPassed) {
    echo "<h3 style='color: green;'>✅ All Tests Passed!</h3>";
    echo "<p>The login system should now work correctly. Try logging in with:</p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Login Page</a>";
    echo "<a href='simple_fix_login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Simple Login</a>";
    echo "</div>";
} else {
    echo "<h3 style='color: red;'>❌ Some Tests Failed</h3>";
    echo "<p>Please address the failed tests before attempting to login.</p>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='setup_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Setup Database</a>";
    echo "<a href='simple_fix_login.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Try Simple Login</a>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Current CSRF Token: " . substr($_SESSION['csrf_token'], 0, 20) . "...</small></p>";
?>

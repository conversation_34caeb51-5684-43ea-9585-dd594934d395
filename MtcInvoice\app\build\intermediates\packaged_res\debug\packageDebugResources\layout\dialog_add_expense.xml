<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <EditText
        android:id="@+id/etAmount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Amount"
        android:inputType="numberDecimal" />

    <EditText
        android:id="@+id/etCategory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Category" />

    <EditText
        android:id="@+id/etDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Date"
        android:inputType="date"
        android:focusable="false" />

    <EditText
        android:id="@+id/etNote"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Note"
        android:inputType="text" />

    <Button
        android:id="@+id/btnPickDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Pick Date" />

</LinearLayout>

<?php
// Final comprehensive login test
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Final Login System Test</h2>";

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$results = [];

// Test 1: Simulate the exact login process
echo "<h3>1. Testing Complete Login Flow</h3>";

try {
    // Prepare login data exactly as the frontend does
    $loginData = [
        'username' => 'admin',
        'password' => 'admin123',
        'remember_me' => 0,
        'csrf_token' => $_SESSION['csrf_token']
    ];
    
    // Test API endpoint with cURL
    $apiUrl = 'http://192.168.0.106/MtcInvoiceMasudvi/mtcadminpanel/api/auth/login';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'X-CSRF-Token: ' . $_SESSION['csrf_token']
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_COOKIEJAR, '/tmp/cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, '/tmp/cookies.txt');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    
    if ($error) {
        echo "<p style='color: red;'>❌ <strong>cURL Error:</strong> $error</p>";
        $results['api_login'] = false;
    } else {
        echo "<p><strong>Raw Response:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($response) . "</pre>";
        
        $responseData = json_decode($response, true);
        
        if ($responseData && isset($responseData['success'])) {
            if ($responseData['success']) {
                echo "<p style='color: green;'>✅ <strong>API Login Successful!</strong></p>";
                echo "<p><strong>Message:</strong> " . htmlspecialchars($responseData['message']) . "</p>";
                if (isset($responseData['data']['redirect'])) {
                    echo "<p><strong>Redirect URL:</strong> " . htmlspecialchars($responseData['data']['redirect']) . "</p>";
                }
                $results['api_login'] = true;
            } else {
                echo "<p style='color: red;'>❌ <strong>API Login Failed:</strong> " . htmlspecialchars($responseData['message']) . "</p>";
                $results['api_login'] = false;
            }
        } else {
            echo "<p style='color: red;'>❌ <strong>Invalid API Response Format</strong></p>";
            $results['api_login'] = false;
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Exception:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    $results['api_login'] = false;
}

// Test 2: Check session after login
echo "<hr><h3>2. Testing Session Management</h3>";

if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✅ <strong>User Session Active</strong></p>";
    echo "<ul>";
    echo "<li><strong>User ID:</strong> " . $_SESSION['user_id'] . "</li>";
    echo "<li><strong>Username:</strong> " . ($_SESSION['username'] ?? 'N/A') . "</li>";
    echo "<li><strong>Role:</strong> " . ($_SESSION['role'] ?? 'N/A') . "</li>";
    echo "<li><strong>Login Time:</strong> " . date('Y-m-d H:i:s', $_SESSION['login_time'] ?? time()) . "</li>";
    echo "</ul>";
    $results['session'] = true;
} else {
    echo "<p style='color: orange;'>⚠️ <strong>No Active Session</strong> (This is normal for API testing)</p>";
    $results['session'] = false;
}

// Test 3: Test dashboard accessibility
echo "<hr><h3>3. Testing Dashboard Access</h3>";

$dashboardUrl = 'http://192.168.0.106/MtcInvoiceMasudvi/mtcadminpanel/dashboard.php';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $dashboardUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEFILE, '/tmp/cookies.txt');

$dashboardResponse = curl_exec($ch);
$dashboardHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>Dashboard HTTP Code:</strong> $dashboardHttpCode</p>";

if ($dashboardHttpCode == 200) {
    if (strpos($dashboardResponse, 'Dashboard') !== false && strpos($dashboardResponse, 'login') === false) {
        echo "<p style='color: green;'>✅ <strong>Dashboard Accessible</strong></p>";
        $results['dashboard'] = true;
    } else {
        echo "<p style='color: orange;'>⚠️ <strong>Dashboard Redirected to Login</strong> (Expected for session-based access)</p>";
        $results['dashboard'] = false;
    }
} else {
    echo "<p style='color: red;'>❌ <strong>Dashboard Not Accessible</strong> (HTTP $dashboardHttpCode)</p>";
    $results['dashboard'] = false;
}

// Test 4: Manual login simulation
echo "<hr><h3>4. Manual Login Simulation</h3>";

try {
    require_once 'config/config.php';
    require_once 'models/User.php';
    
    $userModel = new User();
    $user = $userModel->authenticate('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✅ <strong>Direct Authentication Works</strong></p>";
        echo "<ul>";
        echo "<li><strong>User ID:</strong> " . $user['id'] . "</li>";
        echo "<li><strong>Username:</strong> " . $user['username'] . "</li>";
        echo "<li><strong>Email:</strong> " . $user['email'] . "</li>";
        echo "<li><strong>Role:</strong> " . $user['role'] . "</li>";
        echo "</ul>";
        $results['direct_auth'] = true;
    } else {
        echo "<p style='color: red;'>❌ <strong>Direct Authentication Failed</strong></p>";
        $results['direct_auth'] = false;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ <strong>Direct Authentication Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    $results['direct_auth'] = false;
}

// Summary
echo "<hr><h3>Test Summary</h3>";

$passedTests = array_sum($results);
$totalTests = count($results);

echo "<div style='background: " . ($passedTests == $totalTests ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4 style='margin: 0; color: " . ($passedTests == $totalTests ? '#155724' : '#721c24') . ";'>";
echo ($passedTests == $totalTests ? '✅' : '❌') . " Test Results: $passedTests/$totalTests Passed";
echo "</h4>";

if ($passedTests == $totalTests) {
    echo "<p style='margin: 10px 0 0 0; color: #155724;'>All tests passed! The login system should work correctly.</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #721c24;'>Some tests failed. Please check the issues above.</p>";
}
echo "</div>";

// Action buttons
echo "<div style='margin-top: 30px;'>";
echo "<a href='login.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔐 Try Main Login</a>";
echo "<a href='simple_fix_login.php' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🚀 Try Simple Login</a>";
echo "<a href='verify_login_fix.php' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>🔍 Run Verification</a>";
echo "</div>";

echo "<hr>";
echo "<p><small><strong>Current CSRF Token:</strong> " . substr($_SESSION['csrf_token'], 0, 30) . "...</small></p>";
echo "<p><small><strong>Session ID:</strong> " . session_id() . "</small></p>";
?>

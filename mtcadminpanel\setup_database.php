<?php
// Database setup script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Setup for MTC Invoice System</h2>";

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'invoice_masudvi';
    $username = 'root';
    $password = '';
    
    // Connect without database first
    $dsn = "mysql:host={$host};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to MySQL server<br>";
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '{$dbname}' created/verified<br>";
    
    // Use the database
    $pdo->exec("USE `{$dbname}`");
    echo "✅ Using database '{$dbname}'<br>";
    
    // Create users table
    $createUsersTable = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('admin', 'manager', 'user') DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL
    )";
    
    $pdo->exec($createUsersTable);
    echo "✅ Users table created/verified<br>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        // Create admin user with proper password hash
        $adminPassword = 'admin123';
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        
        $insertAdmin = "INSERT INTO users (username, email, password_hash, full_name, role, is_active) 
                       VALUES ('admin', '<EMAIL>', ?, 'System Administrator', 'admin', 1)";
        
        $stmt = $pdo->prepare($insertAdmin);
        $stmt->execute([$hashedPassword]);
        
        echo "✅ Admin user created successfully<br>";
        echo "   Username: admin<br>";
        echo "   Password: admin123<br>";
        echo "   Email: <EMAIL><br>";
    } else {
        echo "ℹ️ Admin user already exists<br>";
        
        // Verify password
        $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE username = 'admin'");
        $stmt->execute();
        $storedHash = $stmt->fetchColumn();
        
        if (password_verify('admin123', $storedHash)) {
            echo "✅ Admin password is correct (admin123)<br>";
        } else {
            echo "❌ Admin password doesn't match 'admin123'. Updating...<br>";
            
            // Update password
            $newHash = password_hash('admin123', PASSWORD_DEFAULT);
            $updateStmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
            $updateStmt->execute([$newHash]);
            
            echo "✅ Admin password updated to 'admin123'<br>";
        }
    }
    
    // Create other essential tables
    $createInvoiceDataTable = "
    CREATE TABLE IF NOT EXISTS invoice_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firebase_id VARCHAR(100) NULL,
        description TEXT NOT NULL,
        location VARCHAR(255) NOT NULL,
        qr VARCHAR(255) NOT NULL,
        lpo VARCHAR(255) NOT NULL,
        inb VARCHAR(255) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        w_a VARCHAR(255) NOT NULL,
        payment_status ENUM('paid', 'pending', 'overdue', 'cancelled') DEFAULT 'pending',
        timestamp BIGINT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $pdo->exec($createInvoiceDataTable);
    echo "✅ Invoice data table created/verified<br>";
    
    // Create transactions table
    $createTransactionsTable = "
    CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        amount DECIMAL(10,2) NOT NULL,
        category VARCHAR(100) NOT NULL,
        date DATE NOT NULL,
        note TEXT,
        type ENUM('income', 'expense') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    $pdo->exec($createTransactionsTable);
    echo "✅ Transactions table created/verified<br>";
    
    echo "<hr>";
    echo "<h3>✅ Database setup completed successfully!</h3>";
    echo "<p>You can now login with:</p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='simple_fix_login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login</a> ";
    echo "<a href='debug_login.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Debug Login</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>

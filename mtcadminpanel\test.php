<?php
// Simple test file to check PHP and database connectivity
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP Test</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current directory: " . __DIR__ . "</p>";
echo "<p>Document root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

// Test basic database connection without classes
try {
    $host = 'localhost';
    $dbname = 'invoice_masudvi';
    $username = 'root';
    $password = '';

    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<p style='color: green;'>✅ Direct database connection successful!</p>";

    // Test if tables exist
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<h2>Database Tables:</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . $table . "</li>";
    }
    echo "</ul>";

    // Test users table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Users in database: " . $userCount['count'] . "</p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test class loading
try {
    require_once 'config/database.php';
    $db = new Database();
    $connection = $db->getConnection();
    echo "<p style='color: green;'>✅ Database class connection successful!</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database class failed: " . $e->getMessage() . "</p>";
}

echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";
?>

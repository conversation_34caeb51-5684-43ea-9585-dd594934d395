        <?php if (AuthMiddleware::isAuthenticated() && basename($_SERVER['PHP_SELF']) !== 'login.php'): ?>
        </div> <!-- End content-wrapper -->
    </div> <!-- End main-content -->
    <?php endif; ?>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Global variables
        const API_BASE_URL = 'api/';
        const CSRF_TOKEN = '<?php echo $_SESSION['csrf_token'] ?? ''; ?>';
        
        // Utility functions
        function showToast(message, type = 'success') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();
            
            const toastHTML = `
                <div class="toast align-items-center text-white bg-${type} border-0" role="alert" id="${toastId}">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHTML);
            
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 5000
            });
            
            toast.show();
            
            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
            });
        }
        
        function showLoading(show = true) {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(element => {
                element.style.display = show ? 'block' : 'none';
            });
        }
        
        function formatCurrency(amount) {
            return '৳ ' + parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        function formatDate(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleDateString('en-GB') + ' ' + date.toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        }
        
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch(API_BASE_URL + 'auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': CSRF_TOKEN
                    },
                    body: JSON.stringify({
                        csrf_token: CSRF_TOKEN
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = 'login.php';
                    } else {
                        showToast(data.message || 'Logout failed', 'danger');
                    }
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    // Force redirect even if API call fails
                    window.location.href = 'login.php';
                });
            }
        }
        
        // API helper function
        function apiCall(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': CSRF_TOKEN
                }
            };
            
            if (options.body && typeof options.body === 'object') {
                options.body.csrf_token = CSRF_TOKEN;
                options.body = JSON.stringify(options.body);
            }
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            return fetch(API_BASE_URL + endpoint, finalOptions)
                .then(response => {
                    if (response.status === 401) {
                        window.location.href = 'login.php';
                        return;
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('API Error:', error);
                    throw error;
                });
        }
        
        // Form validation helper
        function validateForm(formElement) {
            const requiredFields = formElement.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
        
        // Auto-hide sidebar on mobile when clicking outside
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const toggleButton = event.target.closest('[onclick="toggleSidebar()"]');
                
                if (!sidebar.contains(event.target) && !toggleButton && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // Session timeout warning
        let sessionWarningShown = false;
        
        function checkSession() {
            apiCall('auth/check-session')
                .then(data => {
                    if (data && data.success) {
                        const remainingTime = data.data.remaining_time;
                        
                        // Show warning when 5 minutes remaining
                        if (remainingTime <= 300 && !sessionWarningShown) {
                            sessionWarningShown = true;
                            showToast('Your session will expire in 5 minutes. Please save your work.', 'warning');
                        }
                        
                        // Auto-logout when session expires
                        if (remainingTime <= 0) {
                            showToast('Session expired. Redirecting to login...', 'danger');
                            setTimeout(() => {
                                window.location.href = 'login.php?expired=1';
                            }, 2000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Session check failed:', error);
                });
        }
        
        // Check session every 5 minutes
        if (window.location.pathname.indexOf('login.php') === -1) {
            setInterval(checkSession, 300000); // 5 minutes
        }
        
        // Prevent form resubmission on page refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    </script>
    
    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
</body>
</html>

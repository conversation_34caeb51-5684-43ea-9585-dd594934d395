<?php
$pageTitle = 'Invoice Management - MTC Invoice Management System';
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Invoice Management</h1>
    <div>
        <button class="btn btn-outline-secondary me-2" onclick="exportData()">
            <i class="bi bi-download"></i> Export
        </button>
        <button class="btn btn-primary" onclick="showCreateModal()">
            <i class="bi bi-plus-circle"></i> Add Invoice
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="searchFilter" class="form-label">Search</label>
                <input type="text" class="form-control" id="searchFilter" placeholder="Search invoices...">
            </div>
            <div class="col-md-2">
                <label for="statusFilter" class="form-label">Status</label>
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="paid">Paid</option>
                    <option value="pending">Pending</option>
                    <option value="overdue">Overdue</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="locationFilter" class="form-label">Location</label>
                <input type="text" class="form-control" id="locationFilter" placeholder="Location">
            </div>
            <div class="col-md-2">
                <label for="dateFromFilter" class="form-label">Date From</label>
                <input type="date" class="form-control" id="dateFromFilter">
            </div>
            <div class="col-md-2">
                <label for="dateToFilter" class="form-label">Date To</label>
                <input type="date" class="form-control" id="dateToFilter">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Loading Spinner -->
<div class="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Loading invoice data...</p>
</div>

<!-- Invoice Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="invoicesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Description</th>
                        <th>Location</th>
                        <th>QR</th>
                        <th>LPO</th>
                        <th>INB</th>
                        <th>Amount</th>
                        <th>W/A</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <nav aria-label="Invoice pagination" id="paginationContainer">
            <!-- Pagination will be loaded here -->
        </nav>
    </div>
</div>

<!-- Create/Edit Modal -->
<div class="modal fade" id="invoiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Add New Invoice</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="invoiceForm">
                <div class="modal-body">
                    <input type="hidden" id="invoiceId" name="id">
                    
                    <div class="row g-3">
                        <div class="col-md-12">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location *</label>
                            <input type="text" class="form-control" id="location" name="location" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="qr" class="form-label">QR *</label>
                            <input type="text" class="form-control" id="qr" name="qr" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="lpo" class="form-label">LPO *</label>
                            <input type="text" class="form-control" id="lpo" name="lpo" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="inb" class="form-label">INB *</label>
                            <input type="text" class="form-control" id="inb" name="inb" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="amount" class="form-label">Amount *</label>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="w_a" class="form-label">W/A *</label>
                            <input type="text" class="form-control" id="w_a" name="w_a" required>
                        </div>
                        
                        <div class="col-md-12">
                            <label for="payment_status" class="form-label">Payment Status *</label>
                            <select class="form-select" id="payment_status" name="payment_status" required>
                                <option value="">Select Status</option>
                                <option value="paid">Paid</option>
                                <option value="pending">Pending</option>
                                <option value="overdue">Overdue</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="saveBtn">
                        <span class="spinner-border spinner-border-sm d-none" id="saveSpinner"></span>
                        Save Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Modal -->
<div class="modal fade" id="viewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Invoice Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewModalBody">
                <!-- Invoice details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="editFromView()">Edit</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentFilters = {};
let currentInvoiceId = null;

document.addEventListener('DOMContentLoaded', function() {
    loadInvoices();
    
    // Filter form submission
    document.getElementById('filterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadInvoices();
    });
    
    // Invoice form submission
    document.getElementById('invoiceForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveInvoice();
    });
});

function loadInvoices() {
    showLoading(true);
    
    // Get filter values
    currentFilters = {
        search: document.getElementById('searchFilter').value,
        payment_status: document.getElementById('statusFilter').value,
        location: document.getElementById('locationFilter').value,
        date_from: document.getElementById('dateFromFilter').value,
        date_to: document.getElementById('dateToFilter').value,
        page: currentPage,
        limit: 25
    };
    
    // Remove empty filters
    Object.keys(currentFilters).forEach(key => {
        if (currentFilters[key] === '') {
            delete currentFilters[key];
        }
    });
    
    const queryString = new URLSearchParams(currentFilters).toString();
    
    apiCall(`invoice/index?${queryString}`)
        .then(response => {
            if (response.success) {
                renderInvoicesTable(response.data.data);
                renderPagination(response.data.pagination);
            } else {
                showToast(response.message || 'Failed to load invoices', 'danger');
            }
        })
        .catch(error => {
            console.error('Load invoices error:', error);
            showToast('Failed to load invoices', 'danger');
        })
        .finally(() => {
            showLoading(false);
        });
}

function renderInvoicesTable(invoices) {
    const tbody = document.querySelector('#invoicesTable tbody');

    if (invoices.length === 0) {
        tbody.innerHTML = '<tr><td colspan="11" class="text-center text-muted">No invoices found</td></tr>';
        return;
    }

    tbody.innerHTML = invoices.map(invoice => {
        const statusBadge = getStatusBadge(invoice.payment_status);
        return `
            <tr>
                <td>${invoice.id}</td>
                <td>
                    <div class="fw-medium">${invoice.description.substring(0, 50)}${invoice.description.length > 50 ? '...' : ''}</div>
                </td>
                <td>${invoice.location}</td>
                <td>${invoice.qr}</td>
                <td>${invoice.lpo}</td>
                <td>${invoice.inb}</td>
                <td class="fw-medium">${invoice.formatted_amount}</td>
                <td>${invoice.w_a}</td>
                <td>${statusBadge}</td>
                <td>
                    <small>${invoice.formatted_date}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewInvoice(${invoice.id})" title="View">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="editInvoice(${invoice.id})" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteInvoice(${invoice.id})" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

function renderPagination(pagination) {
    const container = document.getElementById('paginationContainer');

    if (pagination.total_pages <= 1) {
        container.innerHTML = '';
        return;
    }

    let paginationHTML = '<ul class="pagination justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="changePage(${pagination.current_page - 1})">Previous</a>
        </li>`;
    } else {
        paginationHTML += '<li class="page-item disabled"><span class="page-link">Previous</span></li>';
    }

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    if (startPage > 1) {
        paginationHTML += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1)">1</a></li>';
        if (startPage > 2) {
            paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        if (i === pagination.current_page) {
            paginationHTML += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${i})">${i}</a></li>`;
        }
    }

    if (endPage < pagination.total_pages) {
        if (endPage < pagination.total_pages - 1) {
            paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${pagination.total_pages})">${pagination.total_pages}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        paginationHTML += `<li class="page-item">
            <a class="page-link" href="#" onclick="changePage(${pagination.current_page + 1})">Next</a>
        </li>`;
    } else {
        paginationHTML += '<li class="page-item disabled"><span class="page-link">Next</span></li>';
    }

    paginationHTML += '</ul>';
    container.innerHTML = paginationHTML;
}

function changePage(page) {
    currentPage = page;
    loadInvoices();
}

function showCreateModal() {
    currentInvoiceId = null;
    document.getElementById('modalTitle').textContent = 'Add New Invoice';
    document.getElementById('invoiceForm').reset();
    document.getElementById('invoiceId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));
    modal.show();
}

function editInvoice(id) {
    currentInvoiceId = id;
    document.getElementById('modalTitle').textContent = 'Edit Invoice';

    apiCall(`invoice/show?id=${id}`)
        .then(response => {
            if (response.success) {
                const invoice = response.data;

                document.getElementById('invoiceId').value = invoice.id;
                document.getElementById('description').value = invoice.description;
                document.getElementById('location').value = invoice.location;
                document.getElementById('qr').value = invoice.qr;
                document.getElementById('lpo').value = invoice.lpo;
                document.getElementById('inb').value = invoice.inb;
                document.getElementById('amount').value = invoice.amount;
                document.getElementById('w_a').value = invoice.w_a;
                document.getElementById('payment_status').value = invoice.payment_status;

                const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));
                modal.show();
            } else {
                showToast(response.message || 'Failed to load invoice', 'danger');
            }
        })
        .catch(error => {
            console.error('Edit invoice error:', error);
            showToast('Failed to load invoice', 'danger');
        });
}

function viewInvoice(id) {
    apiCall(`invoice/show?id=${id}`)
        .then(response => {
            if (response.success) {
                const invoice = response.data;
                currentInvoiceId = id;

                const modalBody = document.getElementById('viewModalBody');
                modalBody.innerHTML = `
                    <div class="row g-3">
                        <div class="col-md-6">
                            <strong>ID:</strong> ${invoice.id}
                        </div>
                        <div class="col-md-6">
                            <strong>Status:</strong> ${getStatusBadge(invoice.payment_status)}
                        </div>
                        <div class="col-md-12">
                            <strong>Description:</strong><br>
                            ${invoice.description}
                        </div>
                        <div class="col-md-6">
                            <strong>Location:</strong> ${invoice.location}
                        </div>
                        <div class="col-md-6">
                            <strong>QR:</strong> ${invoice.qr}
                        </div>
                        <div class="col-md-6">
                            <strong>LPO:</strong> ${invoice.lpo}
                        </div>
                        <div class="col-md-6">
                            <strong>INB:</strong> ${invoice.inb}
                        </div>
                        <div class="col-md-6">
                            <strong>Amount:</strong> ${invoice.formatted_amount}
                        </div>
                        <div class="col-md-6">
                            <strong>W/A:</strong> ${invoice.w_a}
                        </div>
                        <div class="col-md-12">
                            <strong>Date:</strong> ${invoice.formatted_date}
                        </div>
                    </div>
                `;

                const modal = new bootstrap.Modal(document.getElementById('viewModal'));
                modal.show();
            } else {
                showToast(response.message || 'Failed to load invoice', 'danger');
            }
        })
        .catch(error => {
            console.error('View invoice error:', error);
            showToast('Failed to load invoice', 'danger');
        });
}

function editFromView() {
    const viewModal = bootstrap.Modal.getInstance(document.getElementById('viewModal'));
    viewModal.hide();

    setTimeout(() => {
        editInvoice(currentInvoiceId);
    }, 300);
}

function saveInvoice() {
    const form = document.getElementById('invoiceForm');

    if (!validateForm(form)) {
        showToast('Please fill in all required fields', 'warning');
        return;
    }

    const saveBtn = document.getElementById('saveBtn');
    const saveSpinner = document.getElementById('saveSpinner');

    saveBtn.disabled = true;
    saveSpinner.classList.remove('d-none');

    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    const isEdit = currentInvoiceId !== null;
    const endpoint = isEdit ? 'invoice/update' : 'invoice/create';

    apiCall(endpoint, {
        method: 'POST',
        body: data
    })
    .then(response => {
        if (response.success) {
            showToast(response.message || (isEdit ? 'Invoice updated successfully' : 'Invoice created successfully'), 'success');

            const modal = bootstrap.Modal.getInstance(document.getElementById('invoiceModal'));
            modal.hide();

            loadInvoices();
        } else {
            showToast(response.message || 'Failed to save invoice', 'danger');
        }
    })
    .catch(error => {
        console.error('Save invoice error:', error);
        showToast('Failed to save invoice', 'danger');
    })
    .finally(() => {
        saveBtn.disabled = false;
        saveSpinner.classList.add('d-none');
    });
}

function deleteInvoice(id) {
    if (!confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
        return;
    }

    apiCall('invoice/delete', {
        method: 'POST',
        body: { id: id }
    })
    .then(response => {
        if (response.success) {
            showToast(response.message || 'Invoice deleted successfully', 'success');
            loadInvoices();
        } else {
            showToast(response.message || 'Failed to delete invoice', 'danger');
        }
    })
    .catch(error => {
        console.error('Delete invoice error:', error);
        showToast('Failed to delete invoice', 'danger');
    });
}

function exportData() {
    const filters = { ...currentFilters };
    delete filters.page;
    delete filters.limit;

    const queryString = new URLSearchParams(filters).toString();
    const exportUrl = `api/invoice/export?format=csv&${queryString}`;

    // Create a temporary link and click it to download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = 'invoice_data.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast('Export started. Download will begin shortly.', 'info');
}

function getStatusBadge(status) {
    const badges = {
        'paid': '<span class="badge bg-success">Paid</span>',
        'pending': '<span class="badge bg-warning">Pending</span>',
        'overdue': '<span class="badge bg-danger">Overdue</span>',
        'cancelled': '<span class="badge bg-secondary">Cancelled</span>'
    };

    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}
</script>

<?php include 'includes/footer.php'; ?>

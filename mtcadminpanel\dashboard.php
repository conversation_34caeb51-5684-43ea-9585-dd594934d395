<?php
$pageTitle = 'Dashboard - MTC Invoice Management System';
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Dashboard</h1>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Loading Spinner -->
<div class="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Loading dashboard data...</p>
</div>

<!-- Statistics Cards -->
<div class="row mb-4" id="statsCards">
    <!-- Stats will be loaded here -->
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> Monthly Invoice Trend
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyTrendChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i> Payment Status
                </h5>
            </div>
            <div class="card-body">
                <canvas id="paymentStatusChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> Recent Invoices
                </h5>
                <a href="invoices.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recentInvoicesTable">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>Location</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="invoices.php?action=create" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add New Invoice
                    </a>
                    <a href="files.php?action=upload" class="btn btn-outline-secondary">
                        <i class="bi bi-upload"></i> Upload File
                    </a>
                    <a href="reports.php" class="btn btn-outline-info">
                        <i class="bi bi-file-earmark-text"></i> Generate Report
                    </a>
                    <?php if (AuthMiddleware::hasRole(['admin'])): ?>
                    <a href="users.php?action=create" class="btn btn-outline-success">
                        <i class="bi bi-person-plus"></i> Add User
                    </a>
                    <?php endif; ?>
                </div>
                
                <hr class="my-3">
                
                <h6 class="text-muted">System Info</h6>
                <small class="text-muted">
                    <div class="d-flex justify-content-between">
                        <span>Version:</span>
                        <span><?php echo APP_VERSION; ?></span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>User:</span>
                        <span><?php echo htmlspecialchars($currentUser['username']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Role:</span>
                        <span class="badge bg-primary"><?php echo ucfirst($currentUser['role']); ?></span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Last Login:</span>
                        <span><?php echo $currentUser['last_login'] ? date('d/m/Y H:i', strtotime($currentUser['last_login'])) : 'N/A'; ?></span>
                    </div>
                </small>
            </div>
        </div>
    </div>
</div>

<script>
let monthlyTrendChart, paymentStatusChart;

document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
});

function loadDashboardData() {
    showLoading(true);
    
    Promise.all([
        apiCall('invoice/stats'),
        apiCall('invoice/index?limit=10')
    ])
    .then(([statsResponse, recentResponse]) => {
        if (statsResponse.success) {
            renderStatsCards(statsResponse.data);
            renderCharts(statsResponse.data);
        }
        
        if (recentResponse.success) {
            renderRecentInvoices(recentResponse.data.data);
        }
    })
    .catch(error => {
        console.error('Dashboard load error:', error);
        showToast('Failed to load dashboard data', 'danger');
    })
    .finally(() => {
        showLoading(false);
    });
}

function renderStatsCards(stats) {
    const statsContainer = document.getElementById('statsCards');
    
    const cards = [
        {
            title: 'Total Invoices',
            value: stats.total_invoices.toLocaleString(),
            icon: 'bi-receipt-cutoff',
            color: 'primary',
            change: '+12%'
        },
        {
            title: 'Total Amount',
            value: stats.formatted_total_amount,
            icon: 'bi-currency-dollar',
            color: 'success',
            change: '+8%'
        },
        {
            title: 'Paid Invoices',
            value: (stats.by_status.paid?.count || 0).toLocaleString(),
            icon: 'bi-check-circle',
            color: 'info',
            change: '+15%'
        },
        {
            title: 'Pending Invoices',
            value: (stats.by_status.pending?.count || 0).toLocaleString(),
            icon: 'bi-clock',
            color: 'warning',
            change: '-5%'
        }
    ];
    
    statsContainer.innerHTML = cards.map(card => `
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card ${card.color}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">${card.value}</h3>
                        <p class="mb-0">${card.title}</p>
                        <small class="opacity-75">${card.change} from last month</small>
                    </div>
                    <div>
                        <i class="${card.icon}" style="font-size: 2.5rem; opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderCharts(stats) {
    // Monthly Trend Chart
    const monthlyCtx = document.getElementById('monthlyTrendChart').getContext('2d');

    if (monthlyTrendChart) {
        monthlyTrendChart.destroy();
    }

    const monthlyData = stats.monthly_trend || [];
    const months = monthlyData.map(item => {
        const date = new Date(item.month + '-01');
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    const counts = monthlyData.map(item => item.count);
    const amounts = monthlyData.map(item => item.amount);

    monthlyTrendChart = new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Invoice Count',
                data: counts,
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: 'Total Amount',
                data: amounts,
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Invoice Count'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Amount (৳)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });

    // Payment Status Chart
    const statusCtx = document.getElementById('paymentStatusChart').getContext('2d');

    if (paymentStatusChart) {
        paymentStatusChart.destroy();
    }

    const statusData = stats.by_status || {};
    const statusLabels = Object.keys(statusData);
    const statusCounts = statusLabels.map(status => statusData[status].count || 0);
    const statusColors = {
        'paid': '#27ae60',
        'pending': '#f39c12',
        'overdue': '#e74c3c',
        'cancelled': '#95a5a6'
    };

    paymentStatusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
            datasets: [{
                data: statusCounts,
                backgroundColor: statusLabels.map(status => statusColors[status] || '#95a5a6'),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom'
                }
            }
        }
    });
}

function renderRecentInvoices(invoices) {
    const tbody = document.querySelector('#recentInvoicesTable tbody');

    if (invoices.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No recent invoices found</td></tr>';
        return;
    }

    tbody.innerHTML = invoices.map(invoice => {
        const statusBadge = getStatusBadge(invoice.payment_status);
        return `
            <tr>
                <td>
                    <div class="fw-medium">${invoice.description}</div>
                    <small class="text-muted">QR: ${invoice.qr}</small>
                </td>
                <td>${invoice.location}</td>
                <td class="fw-medium">${invoice.formatted_amount}</td>
                <td>${statusBadge}</td>
                <td>
                    <small>${invoice.formatted_date}</small>
                </td>
            </tr>
        `;
    }).join('');
}

function getStatusBadge(status) {
    const badges = {
        'paid': '<span class="badge bg-success">Paid</span>',
        'pending': '<span class="badge bg-warning">Pending</span>',
        'overdue': '<span class="badge bg-danger">Overdue</span>',
        'cancelled': '<span class="badge bg-secondary">Cancelled</span>'
    };

    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function refreshDashboard() {
    loadDashboardData();
    showToast('Dashboard refreshed', 'success');
}
</script>

<?php include 'includes/footer.php'; ?>

-- Merging decision tree log ---
manifest
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-66:12
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:1-66:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8723982e73f0538ad9064a132ae4e4b\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d8e03882194388b95cbd278c32707c7\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508b837f7f0c53fe67812187f435b715\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e384db1e2171d7d21f24a78a36196b4\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01de847c649509b64612f62a1856e782\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7450d8ac900e978e1bc521919fb3e7ba\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecf79181c581ed2ca3e8efe784214ca\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04ef15b8129f815ae5ed9b8437902caa\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b26aeea9fe28a6f43951abd27ca6570\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46562c7db0466862796dcac157f2bb1\transformed\jetified-activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5feafc1d202ffb6da8206f83ddd1e7c0\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973f52228839302190e5c41f7fe7a417\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b49f117bea16661a20543cc7875704bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f418c21b3e348d964b3ab3391d082477\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba1476b57f0ad5deda38c9785ef5471\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6754affb88c0699172f7581f61cdf86\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31cc480e174608cda21a1f0e7f5627da\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ee7ce3f72c6f153544ff4daf73056f1\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9669dd384b5bf03beb5a1435b345654e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b49ed9fbbb6edd2d4ca960f636fe196\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abad0eeddcf2cbfd2ab38b5bb5ecad0e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ccbd0ab1b02aaa509541385db138ec3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ff9410e717eeebee029d05a542bc20e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b030b0f97ce9a1a1f0a89e729aa04c1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17e7be9355303cfb1c31e96ff4e706c0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0af8a20c0f497221ce78e157cfd198d3\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c9f0bb24c562a31d6b69f2b967c6c2\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\695ea479573ca1f581a0692a43512c4b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16583a44725e9d490c6e37de7ca10d45\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.github.zcweng:switch-button:0.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a41476bf49f5ca0f0484d015d1109e\transformed\jetified-switch-button-0.0.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3edb8b7c02e5294a3024edb4de901162\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b6ada246cff0802771bf13ffa937d2c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\286d855e0af3932ca21515f8a39241bf\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b14873d396d5ff796eda3d27784b2be\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad09ee995fce0eb36cb8a0f5aabdddb1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\193fbd2bf2615c9197737e7934cd23da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a35ad4898f277f15602234a6b37665c5\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e80a33b1ecba1f8925b0940cc5a920f0\transformed\exifinterface-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb82606e3e52d1ab232e9a37089d9c89\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9501e01ff7a229066fe2cc3b27276\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a55e2ec67c28bbde18595af9e52992d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bd07f150978a2f0c4a9222572cad6b7\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\918ae5457270fecd763dab3b3eb75dd7\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:7:5-9:40
	tools:ignore
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:9:9-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:8:9-66
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:5-76
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:22-73
application
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-64:19
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-64:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8723982e73f0538ad9064a132ae4e4b\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8723982e73f0538ad9064a132ae4e4b\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d8e03882194388b95cbd278c32707c7\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d8e03882194388b95cbd278c32707c7\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508b837f7f0c53fe67812187f435b715\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508b837f7f0c53fe67812187f435b715\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:7:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7450d8ac900e978e1bc521919fb3e7ba\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7450d8ac900e978e1bc521919fb3e7ba\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\193fbd2bf2615c9197737e7934cd23da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\193fbd2bf2615c9197737e7934cd23da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a35ad4898f277f15602234a6b37665c5\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a35ad4898f277f15602234a6b37665c5\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:18:9-41
	android:fullBackupContent
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:16:9-54
	android:roundIcon
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:23:9-29
	android:icon
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:17:9-43
	android:allowBackup
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:14:9-35
	android:theme
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:22:9-48
	android:networkSecurityConfig
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:19:9-69
	android:dataExtractionRules
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:15:9-65
activity#com.official.invoicegenarator.InvoiceTwo
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:24:9-26:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:26:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:25:13-39
activity#com.official.invoicegenarator.InvoiceTraker
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:27:9-29:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:29:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:28:13-42
activity#com.official.invoicegenarator.PdfViewerActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:31:13-46
activity#com.official.invoicegenarator.DownloadListActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:33:9-35:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:35:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:34:13-49
activity#com.official.invoicegenarator.MoneyBagActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:38:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:37:13-45
activity#com.official.invoicegenarator.FingerprintSettingsActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:39:9-41:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:40:13-56
activity#com.official.invoicegenarator.VarifyActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:42:9-44:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:44:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:43:13-43
activity#com.official.invoicegenarator.WorkerAttendenceActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:45:9-47:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:47:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:46:13-53
activity#com.official.invoicegenarator.SelectionActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:49:13-46
activity#com.official.invoicegenarator.Home
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:51:9-54:55
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:54:13-52
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:52:13-33
activity#com.official.invoicegenarator.MainActivity
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:55:9-63:20
	android:exported
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:57:13-36
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:56:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:58:13-62:29
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:17-69
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:17-77
	android:name
		ADDED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:27-74
uses-sdk
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8723982e73f0538ad9064a132ae4e4b\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8723982e73f0538ad9064a132ae4e4b\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d8e03882194388b95cbd278c32707c7\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d8e03882194388b95cbd278c32707c7\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508b837f7f0c53fe67812187f435b715\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\508b837f7f0c53fe67812187f435b715\transformed\jetified-lottie-6.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e384db1e2171d7d21f24a78a36196b4\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e384db1e2171d7d21f24a78a36196b4\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01de847c649509b64612f62a1856e782\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01de847c649509b64612f62a1856e782\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7450d8ac900e978e1bc521919fb3e7ba\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7450d8ac900e978e1bc521919fb3e7ba\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecf79181c581ed2ca3e8efe784214ca\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ecf79181c581ed2ca3e8efe784214ca\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04ef15b8129f815ae5ed9b8437902caa\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04ef15b8129f815ae5ed9b8437902caa\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b26aeea9fe28a6f43951abd27ca6570\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b26aeea9fe28a6f43951abd27ca6570\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46562c7db0466862796dcac157f2bb1\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b46562c7db0466862796dcac157f2bb1\transformed\jetified-activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5feafc1d202ffb6da8206f83ddd1e7c0\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5feafc1d202ffb6da8206f83ddd1e7c0\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973f52228839302190e5c41f7fe7a417\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973f52228839302190e5c41f7fe7a417\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b49f117bea16661a20543cc7875704bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b49f117bea16661a20543cc7875704bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f418c21b3e348d964b3ab3391d082477\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f418c21b3e348d964b3ab3391d082477\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba1476b57f0ad5deda38c9785ef5471\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba1476b57f0ad5deda38c9785ef5471\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6754affb88c0699172f7581f61cdf86\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6754affb88c0699172f7581f61cdf86\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31cc480e174608cda21a1f0e7f5627da\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31cc480e174608cda21a1f0e7f5627da\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ee7ce3f72c6f153544ff4daf73056f1\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ee7ce3f72c6f153544ff4daf73056f1\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9669dd384b5bf03beb5a1435b345654e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9669dd384b5bf03beb5a1435b345654e\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b49ed9fbbb6edd2d4ca960f636fe196\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b49ed9fbbb6edd2d4ca960f636fe196\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abad0eeddcf2cbfd2ab38b5bb5ecad0e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abad0eeddcf2cbfd2ab38b5bb5ecad0e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ccbd0ab1b02aaa509541385db138ec3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ccbd0ab1b02aaa509541385db138ec3\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ff9410e717eeebee029d05a542bc20e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6ff9410e717eeebee029d05a542bc20e\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b030b0f97ce9a1a1f0a89e729aa04c1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b030b0f97ce9a1a1f0a89e729aa04c1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17e7be9355303cfb1c31e96ff4e706c0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17e7be9355303cfb1c31e96ff4e706c0\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0af8a20c0f497221ce78e157cfd198d3\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0af8a20c0f497221ce78e157cfd198d3\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c9f0bb24c562a31d6b69f2b967c6c2\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53c9f0bb24c562a31d6b69f2b967c6c2\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\695ea479573ca1f581a0692a43512c4b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\695ea479573ca1f581a0692a43512c4b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16583a44725e9d490c6e37de7ca10d45\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16583a44725e9d490c6e37de7ca10d45\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.zcweng:switch-button:0.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a41476bf49f5ca0f0484d015d1109e\transformed\jetified-switch-button-0.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.zcweng:switch-button:0.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87a41476bf49f5ca0f0484d015d1109e\transformed\jetified-switch-button-0.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3edb8b7c02e5294a3024edb4de901162\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3edb8b7c02e5294a3024edb4de901162\transformed\jetified-MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b6ada246cff0802771bf13ffa937d2c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b6ada246cff0802771bf13ffa937d2c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\286d855e0af3932ca21515f8a39241bf\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\286d855e0af3932ca21515f8a39241bf\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b14873d396d5ff796eda3d27784b2be\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b14873d396d5ff796eda3d27784b2be\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad09ee995fce0eb36cb8a0f5aabdddb1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad09ee995fce0eb36cb8a0f5aabdddb1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\193fbd2bf2615c9197737e7934cd23da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\193fbd2bf2615c9197737e7934cd23da\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a35ad4898f277f15602234a6b37665c5\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a35ad4898f277f15602234a6b37665c5\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e80a33b1ecba1f8925b0940cc5a920f0\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e80a33b1ecba1f8925b0940cc5a920f0\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb82606e3e52d1ab232e9a37089d9c89\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb82606e3e52d1ab232e9a37089d9c89\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9501e01ff7a229066fe2cc3b27276\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1f9501e01ff7a229066fe2cc3b27276\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a55e2ec67c28bbde18595af9e52992d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a55e2ec67c28bbde18595af9e52992d\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bd07f150978a2f0c4a9222572cad6b7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bd07f150978a2f0c4a9222572cad6b7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\918ae5457270fecd763dab3b3eb75dd7\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\918ae5457270fecd763dab3b3eb75dd7\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f977c61f7b35470b633cc289f698e4e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92

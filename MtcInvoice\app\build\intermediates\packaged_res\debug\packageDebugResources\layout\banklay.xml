<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >


    <EditText
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginBottom="20dp"
        android:textSize="14sp"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewromanbold"
        android:background="@null"
        android:textColor="@color/black"
        android:text="Banking Account Details" />

    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:stretchColumns="1">

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >

            <EditText
                android:background="@drawable/cell_background"
                android:id="@+id/bank_name_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="5dp"
                android:textSize="12sp"
                android:fontFamily="@font/timesnewromanbold"
                android:text="Bank Name"
                android:textStyle="bold"
                android:gravity="center"
                />

            <EditText
                android:background="@drawable/cell_background"
                android:id="@+id/bank_name_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="5dp"
                android:textSize="12sp"
                android:fontFamily="@font/timesnewromanbold"
                android:text="Bank Muscat"
                android:gravity="center"
                />
        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/cell_background">

            <EditText
                android:id="@+id/account_no_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/cell_background"
                android:padding="5dp"
                android:text="Account No"
                android:textSize="12sp"
                android:fontFamily="@font/timesnewromanbold"
                android:textStyle="bold"
                android:gravity="center"
                />

            <EditText
                android:id="@+id/account_no_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="5dp"
                android:background="@drawable/cell_background"
                android:text="****************"
                android:textSize="12sp"
                android:fontFamily="@font/timesnewromanbold"
                android:gravity="center"
                />
        </TableRow>

        <TableRow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >

            <EditText
                android:id="@+id/account_name_holder_label"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:padding="5dp"
                android:text="Account name holder"
                android:textSize="12sp"
                android:fontFamily="@font/timesnewromanbold"
                android:background="@drawable/cell_background"
                android:textStyle="bold"
                android:gravity="center"
                />

            <EditText
                android:id="@+id/account_name_holder_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="5dp"
                android:background="@drawable/cell_background"
                android:text="NABRAS AL KHOUDH TRAD &amp; CON. PROJECT"
                android:textSize="12sp"
                android:fontFamily="@font/timesnewromanbold"
                android:gravity="center"
                />
        </TableRow>

    </TableLayout>

</LinearLayout>
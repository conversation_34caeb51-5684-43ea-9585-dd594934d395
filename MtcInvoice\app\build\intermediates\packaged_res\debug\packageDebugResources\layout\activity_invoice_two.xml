<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".InvoiceTwo">
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/green"
        app:titleTextColor="@color/white"
        app:popupTheme="@style/ThemeOverlay.AppCompat.DayNight"
        app:title="Second Invoice" />

    <!-- Scrollable content -->
    <ScrollView
        android:id="@+id/sView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottomLayout"
        android:layout_below="@id/toolbar"
        android:fadeScrollbars="true"
        android:scrollbarThumbVertical="@drawable/custom_vertical_thumb"
        android:scrollbars="vertical">

        <!-- Inner layout to hold the scrollable content -->
        <LinearLayout
            android:id="@+id/sllayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="vertical"
            android:paddingLeft="9dp"
            android:paddingRight="9dp">

            <!-- Include Header layout -->
            <include
                android:id="@+id/include"
                layout="@layout/header_main" />

            <!-- Include Content layout -->
            <include layout="@layout/content_main_two" />

            <!-- Include Text Main layout -->
            <include layout="@layout/texmain_two" />

            <!-- Footer Layout -->
            <LinearLayout
                android:id="@+id/linearLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10sp"
                android:layout_marginBottom="50dp">

                <include
                    android:id="@+id/idididi"
                    layout="@layout/footer_main" />
            </LinearLayout>

            <!-- Bank Layout (initially hidden) -->
            <LinearLayout
                android:id="@+id/banklay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="120dp"
                android:visibility="gone">

                <include
                    android:id="@+id/idididi"
                    layout="@layout/banklay" />
            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <!-- Bottom Layout with Zoom Controls and Buttons -->
    <LinearLayout
        android:id="@+id/bottomLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/rounded_corners"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">
            <!-- Zoom Controls -->
            <ZoomControls
                android:id="@+id/zoomControls"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center" />

            <ImageView

                android:id="@+id/resetZoomBtn"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_marginStart="29dp"
                android:src="@drawable/baseline_settings_backup_restore_24"
                app:tint="@color/white" />
            <ImageView
                android:id="@+id/downloads"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_marginStart="29dp"
                android:src="@drawable/downloadlist"
                app:tint="@color/white" />


        </LinearLayout>


        <!-- Action Buttons (Download, Upload, Column Manipulation) -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="5dp">

            <ImageView
                android:id="@+id/downloadBtn"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_weight="1"
                android:src="@drawable/download" />

            <ImageView
                android:id="@+id/uploadBtn"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_weight="1"
                android:src="@drawable/upload" />

            <ImageView
                android:id="@+id/add_column"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:background="@drawable/border_black"
                android:src="@drawable/baseline_add_circle_outline_24" />

            <ImageView
                android:id="@+id/remove_column"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:background="@drawable/border_black"
                android:src="@drawable/baseline_remove_circle_outline_24" />

            <ImageView
                android:id="@+id/add_vat_column"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:background="@drawable/btn_color_before"
                android:src="@drawable/baseline_add_circle_outline_24" />

            <ImageView
                android:id="@+id/remove_vat_column"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:background="@drawable/btn_color_after"
                android:src="@drawable/baseline_remove_circle_outline_24" />
        </LinearLayout>

        <!-- Bank Layout Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="5dp">

            <ImageView
                android:id="@+id/add_back_layout"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:background="@drawable/btn_bankbackg"
                android:src="@drawable/baseline_add_circle_outline_24" />

            <ImageView
                android:id="@+id/remove_back_layout"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                android:background="@drawable/btn_bankr"
                android:src="@drawable/baseline_remove_circle_outline_24" />
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="10dp">

    <!-- Description Field -->
    <TextView
        android:id="@+id/descriptionTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Description"
        android:textStyle="bold"
        android:textSize="16sp"
        android:padding="5dp" />

    <!-- Location Field -->
    <TextView
        android:id="@+id/locationTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Location"
        android:textSize="14sp"
        android:padding="5dp" />

    <!-- QR Code Field -->
    <TextView
        android:id="@+id/qrTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="QR"
        android:textSize="14sp"
        android:padding="5dp" />

    <!-- LPO Field -->
    <TextView
        android:id="@+id/lpoTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="LPO"
        android:textSize="14sp"
        android:padding="5dp" />

    <!-- INB Field -->
    <TextView
        android:id="@+id/inbTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="INB"
        android:textSize="14sp"
        android:padding="5dp" />

    <!-- Amount Field -->
    <TextView
        android:id="@+id/amountTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Amount"
        android:textSize="14sp"
        android:padding="5dp" />

    <!-- W/A Field -->
    <TextView
        android:id="@+id/waTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="W/A"
        android:textSize="14sp"
        android:padding="5dp" />

    <!-- Buttons for Update and Delete -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:paddingTop="10dp">

        <Button
            android:id="@+id/updateButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Update"
            android:padding="8dp"
            android:layout_marginEnd="10dp" />

        <Button
            android:id="@+id/deleteButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Delete"
            android:padding="8dp"
            android:textColor="@android:color/holo_red_dark" />
    </LinearLayout>
</LinearLayout>

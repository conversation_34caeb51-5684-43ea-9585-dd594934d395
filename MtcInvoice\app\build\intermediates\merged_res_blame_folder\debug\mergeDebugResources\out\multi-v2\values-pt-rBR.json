{"logs": [{"outputFile": "com.official.invoicegenarator.app-mergeDebugResources-35:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\49156148c3d3ce629ca1a50fd6c8b45d\\transformed\\biometric-1.1.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,253,370,509,656,787,917,1061,1162,1296,1440", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "154,248,365,504,651,782,912,1056,1157,1291,1435,1558"}, "to": {"startLines": "48,49,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4629,4939,5056,5195,5342,5473,5603,5747,5848,5982,6126", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "4624,4718,5051,5190,5337,5468,5598,5742,5843,5977,6121,6244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c1edbebcd026b7dc912fe9072250b2\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,11489", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,11585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d8723982e73f0538ad9064a132ae4e4b\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,52,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,4723,4783,4847,6249,6328,6388,6478,6542,6613,6676,6751,6815,6869,6996,7054,7116,7170,7249,7390,7477,7553,7648,7729,7811,7950,8033,8117,8256,8343,8423,8479,8530,8596,8670,8750,8821,8904,8977,9054,9123,9197,9299,9387,9464,9557,9653,9727,9807,9904,9956,10040,10106,10193,10281,10343,10407,10470,10538,10647,10758,10862,10972,11032,11087,11250,11333,11410", "endLines": "5,33,34,35,36,37,45,46,47,50,51,52,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,125,126,127", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,4778,4842,4934,6323,6383,6473,6537,6608,6671,6746,6810,6864,6991,7049,7111,7165,7244,7385,7472,7548,7643,7724,7806,7945,8028,8112,8251,8338,8418,8474,8525,8591,8665,8745,8816,8899,8972,9049,9118,9192,9294,9382,9459,9552,9648,9722,9802,9899,9951,10035,10101,10188,10276,10338,10402,10465,10533,10642,10753,10857,10967,11027,11082,11159,11328,11405,11484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\01de847c649509b64612f62a1856e782\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,11164", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,11245"}}]}]}
<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".SelectionActivity">

    <!-- Main content layout -->
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#3C76E9">

        <!-- Material Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:background="#50CADD"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:navigationIconTint="@color/white"
            app:titleTextColor="@color/white"
            app:titleCentered="true"
            app:navigationIcon="@drawable/baseline_menu_24"
            app:title="@string/app_name" />

        <!-- Scrollable content using NestedScrollView -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Inner content layout -->
            <LinearLayout
                android:id="@+id/linearLayout2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="5dp">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginBottom="5dp"
                    android:layout_marginRight="2dp"
                    android:background="@drawable/card_view_elevation"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp">

                    <ImageView
                        android:id="@+id/moneybagImageView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clickable="true"
                        android:focusable="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/moneybag" />

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginBottom="5dp"
                    android:layout_marginLeft="2dp"
                    android:background="@drawable/card_view_elevation"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp">

                    <ImageView
                        android:id="@+id/iv_worker_presentation"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clickable="true"
                        android:focusable="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/workerattendance" />

                </androidx.cardview.widget.CardView>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:orientation="horizontal"
                    android:weightSum="2"
                    android:layout_marginBottom="5dp">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="250dp"
                        android:layout_weight="1"
                        android:layout_marginBottom="5dp"
                        android:layout_marginRight="3dp"
                        android:background="@drawable/card_view_elevation"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:id="@+id/iv_invoice"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clickable="true"
                            android:focusable="true"
                            android:scaleType="centerCrop"
                            android:src="@drawable/first_invoice" />

                    </androidx.cardview.widget.CardView>
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="250dp"
                        android:layout_weight="1"
                        android:layout_marginBottom="5dp"
                        android:background="@drawable/card_view_elevation"
                        app:cardBackgroundColor="@color/white"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:id="@+id/iv_invoice_two"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clickable="true"
                            android:focusable="true"
                            android:scaleType="centerCrop"
                            android:src="@drawable/second_invoice" />

                    </androidx.cardview.widget.CardView>

                </LinearLayout>


                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/card_view_elevation"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp">

                    <ImageView
                        android:id="@+id/invoice_traker"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clickable="true"
                        android:focusable="true"
                        android:scaleType="centerCrop"
                        android:src="@drawable/invoice_traker" />

                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <!-- Navigation drawer -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        style="@style/Theme.AppCompat.NoActionBar"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        app:itemTextColor="@color/white"
        app:itemIconTint="@color/white"
        app:itemBackground="@color/green"
        android:background="@drawable/navigationicon"
        app:headerLayout="@layout/nav_header"
        app:menu="@menu/nav_menu" />

</androidx.drawerlayout.widget.DrawerLayout>

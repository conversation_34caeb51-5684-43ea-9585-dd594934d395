package com.official.invoicegenarator.api;

/**
 * API Configuration
 * MTC Invoice Management System
 */
public class ApiConfig {
    
    // Base URL for the API
    public static final String BASE_URL = "http://192.168.0.106/MtcInvoiceMasudvi/mtcadminpanel/api/";
    
    // API Endpoints
    public static final String INVOICE_CREATE = "invoice/create";
    public static final String INVOICE_UPDATE = "invoice/update";
    public static final String INVOICE_DELETE = "invoice/delete";
    public static final String INVOICE_LIST = "invoice/index";
    public static final String INVOICE_SHOW = "invoice/show";
    public static final String INVOICE_SEARCH = "invoice/search";
    
    public static final String FILE_UPLOAD = "files/upload";
    public static final String FILE_DOWNLOAD = "files/download";
    public static final String FILE_DELETE = "files/delete";
    public static final String FILE_LIST = "files/list";
    
    public static final String AUTH_LOGIN = "auth/login";
    public static final String AUTH_LOGOUT = "auth/logout";
    public static final String AUTH_CHECK = "auth/check-session";
    
    // Request timeouts (in seconds)
    public static final int CONNECT_TIMEOUT = 30;
    public static final int READ_TIMEOUT = 60;
    public static final int WRITE_TIMEOUT = 60;
    
    // File upload settings
    public static final int MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    public static final String[] ALLOWED_FILE_TYPES = {"pdf", "doc", "docx", "jpg", "jpeg", "png"};
    
    // Response codes
    public static final int SUCCESS_CODE = 200;
    public static final int CREATED_CODE = 201;
    public static final int BAD_REQUEST_CODE = 400;
    public static final int UNAUTHORIZED_CODE = 401;
    public static final int FORBIDDEN_CODE = 403;
    public static final int NOT_FOUND_CODE = 404;
    public static final int SERVER_ERROR_CODE = 500;
    
    // Headers
    public static final String CONTENT_TYPE_JSON = "application/json";
    public static final String CONTENT_TYPE_MULTIPART = "multipart/form-data";
    public static final String HEADER_CSRF_TOKEN = "X-CSRF-Token";
    public static final String HEADER_AUTHORIZATION = "Authorization";
    
    /**
     * Get full URL for endpoint
     * @param endpoint The API endpoint
     * @return Full URL
     */
    public static String getFullUrl(String endpoint) {
        return BASE_URL + endpoint;
    }
    
    /**
     * Check if file type is allowed
     * @param fileName The file name
     * @return true if allowed, false otherwise
     */
    public static boolean isFileTypeAllowed(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        for (String allowedType : ALLOWED_FILE_TYPES) {
            if (allowedType.equals(extension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get file extension from filename
     * @param fileName The file name
     * @return File extension
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * Check if file size is within limits
     * @param fileSize File size in bytes
     * @return true if within limits, false otherwise
     */
    public static boolean isFileSizeValid(long fileSize) {
        return fileSize > 0 && fileSize <= MAX_FILE_SIZE;
    }
}

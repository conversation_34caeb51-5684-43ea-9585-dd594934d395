# MTC Invoice Management System - Security Configuration

# Enable URL rewriting
RewriteEngine On

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|old|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
# RewriteRule ^(config|includes|utils|middleware)/ - [F,L]

# Prevent access to PHP files in certain directories
# <LocationMatch "^/(config|includes|utils|middleware)/">
#     Order Allow,Deny
#     Deny from all
# </LocationMatch>

# Security headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' https://cdn.jsdelivr.net; connect-src 'self';"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Disable server signature
# ServerTokens Prod

# Prevent access to version control directories
RedirectMatch 404 /\.git
RedirectMatch 404 /\.svn

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Limit file upload size (10MB)
LimitRequestBody ********

# Prevent execution of PHP files in uploads directory
<Directory "uploads">
    <FilesMatch "\.php$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
    
    # Only allow specific file types
    <FilesMatch "\.(pdf|doc|docx|jpg|jpeg|png|gif)$">
        Order Allow,Deny
        Allow from all
    </FilesMatch>
</Directory>

# API routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Prevent direct access to install.php after installation
<Files "install.php">
    <RequireAll>
        # Require all denied
        # Uncomment the line below to allow access during installation
        Require all granted
    </RequireAll>
</Files>

# Error pages
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /error.php?code=404
ErrorDocument 500 /error.php?code=500

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-component "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
</IfModule>

# Prevent hotlinking
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|pdf)$ - [NC,F,L]

# Rate limiting (if mod_evasive is available)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSSiteCount        50
    DOSPageInterval     1
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

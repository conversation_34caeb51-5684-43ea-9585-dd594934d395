<?php
/**
 * File Controller
 * MTC Invoice Management System
 */

class FileController extends BaseController {
    private $fileModel;
    
    public function __construct() {
        parent::__construct();
        $this->fileModel = new FileUpload();
    }
    
    /**
     * Upload file
     */
    public function upload() {
        try {
            $this->requireAuth();
            $this->requireCSRF();
            
            if (empty($_FILES['file'])) {
                $this->sendError('No file uploaded', 400);
            }
            
            $file = $_FILES['file'];
            $invoiceDataId = $this->request['data']['invoice_data_id'] ?? null;
            
            // Handle file upload
            $uploadResult = $this->handleFileUpload($file);
            
            // Save file record to database
            $fileData = [
                'invoice_data_id' => $invoiceDataId,
                'original_filename' => $uploadResult['original_name'],
                'stored_filename' => $uploadResult['stored_name'],
                'file_path' => $uploadResult['path'],
                'file_size' => $uploadResult['size'],
                'mime_type' => $uploadResult['type'],
                'uploaded_by' => $this->user['id']
            ];
            
            $fileId = $this->fileModel->create($fileData);
            
            if ($fileId) {
                $this->logActivity('UPLOAD_FILE', 'file_uploads', $fileId, null, $fileData);
                
                $this->sendSuccess('File uploaded successfully', [
                    'file_id' => $fileId,
                    'filename' => $uploadResult['original_name'],
                    'size' => $uploadResult['size']
                ], 201);
            } else {
                // Clean up uploaded file if database insert failed
                if (file_exists($uploadResult['path'])) {
                    unlink($uploadResult['path']);
                }
                $this->sendError('Failed to save file record');
            }
            
        } catch (Exception $e) {
            error_log("File upload error: " . $e->getMessage());
            $this->sendError($e->getMessage());
        }
    }
    
    /**
     * Download file
     */
    public function download() {
        try {
            $fileId = $this->request['data']['id'] ?? null;
            
            if (!$fileId) {
                $this->sendError('File ID is required', 400);
            }
            
            $file = $this->fileModel->find($fileId);
            
            if (!$file || !$file['is_active']) {
                $this->sendError('File not found', 404);
            }
            
            $filePath = $file['file_path'];
            
            if (!file_exists($filePath)) {
                $this->sendError('File not found on disk', 404);
            }
            
            // Log download activity
            $this->logActivity('DOWNLOAD_FILE', 'file_uploads', $fileId);
            
            // Set headers for file download
            header('Content-Type: ' . $file['mime_type']);
            header('Content-Disposition: attachment; filename="' . $file['original_filename'] . '"');
            header('Content-Length: ' . filesize($filePath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: 0');
            
            // Output file
            readfile($filePath);
            exit;
            
        } catch (Exception $e) {
            error_log("File download error: " . $e->getMessage());
            $this->sendError('Download failed');
        }
    }
    
    /**
     * Delete file
     */
    public function delete() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin', 'manager']);
            $this->requireCSRF();
            
            $fileId = $this->request['data']['id'] ?? null;
            
            if (!$fileId) {
                $this->sendError('File ID is required', 400);
            }
            
            $file = $this->fileModel->find($fileId);
            
            if (!$file) {
                $this->sendError('File not found', 404);
            }
            
            // Mark as inactive instead of deleting
            $success = $this->fileModel->update($fileId, ['is_active' => 0]);
            
            if ($success) {
                $this->logActivity('DELETE_FILE', 'file_uploads', $fileId, $file, null);
                $this->sendSuccess('File deleted successfully');
            } else {
                $this->sendError('Failed to delete file');
            }
            
        } catch (Exception $e) {
            error_log("File delete error: " . $e->getMessage());
            $this->sendError('Failed to delete file');
        }
    }
    
    /**
     * List files
     */
    public function index() {
        try {
            $this->requireAuth();
            
            $pagination = $this->getPagination();
            $invoiceDataId = $this->request['data']['invoice_data_id'] ?? null;
            
            $conditions = ['is_active' => 1];
            if ($invoiceDataId) {
                $conditions['invoice_data_id'] = $invoiceDataId;
            }
            
            $files = $this->fileModel->getAll($conditions, 'upload_timestamp DESC', $pagination['limit'], ($pagination['page'] - 1) * $pagination['limit']);
            $total = $this->fileModel->count($conditions);
            
            // Format file data
            foreach ($files as &$file) {
                $file['formatted_size'] = $this->formatFileSize($file['file_size']);
                $file['formatted_date'] = date('d/m/Y H:i', strtotime($file['upload_timestamp']));
                unset($file['file_path']); // Don't expose file path
            }
            
            $result = [
                'data' => $files,
                'pagination' => [
                    'current_page' => $pagination['page'],
                    'per_page' => $pagination['limit'],
                    'total' => $total,
                    'total_pages' => ceil($total / $pagination['limit']),
                    'has_next' => $pagination['page'] < ceil($total / $pagination['limit']),
                    'has_prev' => $pagination['page'] > 1
                ]
            ];
            
            $this->sendSuccess('Files retrieved successfully', $result);
            
        } catch (Exception $e) {
            error_log("File list error: " . $e->getMessage());
            $this->sendError('Failed to retrieve files');
        }
    }
    
    /**
     * Get file info
     */
    public function show() {
        try {
            $this->requireAuth();
            
            $fileId = $this->request['data']['id'] ?? null;
            
            if (!$fileId) {
                $this->sendError('File ID is required', 400);
            }
            
            $file = $this->fileModel->find($fileId);
            
            if (!$file || !$file['is_active']) {
                $this->sendError('File not found', 404);
            }
            
            // Format file data
            $file['formatted_size'] = $this->formatFileSize($file['file_size']);
            $file['formatted_date'] = date('d/m/Y H:i', strtotime($file['upload_timestamp']));
            unset($file['file_path']); // Don't expose file path
            
            $this->sendSuccess('File info retrieved successfully', $file);
            
        } catch (Exception $e) {
            error_log("File show error: " . $e->getMessage());
            $this->sendError('Failed to retrieve file info');
        }
    }
    
    /**
     * Format file size
     * @param int $bytes
     * @return string
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

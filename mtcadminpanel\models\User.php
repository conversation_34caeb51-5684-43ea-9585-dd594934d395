<?php
/**
 * User Model
 * MTC Invoice Management System
 */

class User extends BaseModel {
    protected $table = 'users';
    protected $fillable = [
        'username', 'email', 'password_hash', 'full_name', 'role', 'is_active'
    ];
    protected $hidden = ['password_hash'];
    
    /**
     * Authenticate user
     * @param string $username
     * @param string $password
     * @return array|false
     */
    public function authenticate($username, $password) {
        $sql = "SELECT * FROM {$this->table} WHERE (username = ? OR email = ?) AND is_active = 1";
        $user = $this->db->fetchOne($sql, [$username, $username]);
        
        if ($user && password_verify($password, $user['password_hash'])) {
            // Update last login
            $this->update($user['id'], ['last_login' => date('Y-m-d H:i:s')]);
            
            // Remove password hash from returned data
            unset($user['password_hash']);
            return $user;
        }
        
        return false;
    }
    
    /**
     * Create new user
     * @param array $data
     * @return int|false
     */
    public function createUser($data) {
        // Validate required fields
        $required = ['username', 'email', 'password', 'full_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new InvalidArgumentException("Field '{$field}' is required");
            }
        }
        
        // Validate email
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException("Invalid email format");
        }
        
        // Check if username or email already exists
        if ($this->usernameExists($data['username'])) {
            throw new InvalidArgumentException("Username already exists");
        }
        
        if ($this->emailExists($data['email'])) {
            throw new InvalidArgumentException("Email already exists");
        }
        
        // Hash password
        $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
        unset($data['password']);
        
        // Set default role if not provided
        if (empty($data['role'])) {
            $data['role'] = 'user';
        }
        
        return $this->create($data);
    }
    
    /**
     * Update user password
     * @param int $userId
     * @param string $newPassword
     * @return bool
     */
    public function updatePassword($userId, $newPassword) {
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password_hash' => $passwordHash]);
    }
    
    /**
     * Check if username exists
     * @param string $username
     * @param int $excludeId
     * @return bool
     */
    public function usernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return (int) $result['count'] > 0;
    }
    
    /**
     * Check if email exists
     * @param string $email
     * @param int $excludeId
     * @return bool
     */
    public function emailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return (int) $result['count'] > 0;
    }
    
    /**
     * Get user by username or email
     * @param string $identifier
     * @return array|false
     */
    public function getByIdentifier($identifier) {
        $sql = "SELECT * FROM {$this->table} WHERE (username = ? OR email = ?) AND is_active = 1";
        $result = $this->db->fetchOne($sql, [$identifier, $identifier]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return false;
    }
    
    /**
     * Get users with pagination
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getUsers($filters = [], $page = 1, $limit = 25) {
        $offset = ($page - 1) * $limit;
        $conditions = [];
        $params = [];
        $sql = "SELECT * FROM {$this->table}";
        
        // Build WHERE clause based on filters
        if (!empty($filters['search'])) {
            $searchConditions = [
                "username LIKE ?",
                "email LIKE ?",
                "full_name LIKE ?"
            ];
            $searchTerm = "%{$filters['search']}%";
            $conditions[] = "(" . implode(' OR ', $searchConditions) . ")";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        if (!empty($filters['role'])) {
            $conditions[] = "role = ?";
            $params[] = $filters['role'];
        }
        
        if (isset($filters['is_active'])) {
            $conditions[] = "is_active = ?";
            $params[] = $filters['is_active'] ? 1 : 0;
        }
        
        // Add WHERE clause if conditions exist
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        
        // Count total records for pagination
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as filtered_results";
        $totalResult = $this->db->fetchOne($countSql, $params);
        $total = (int) $totalResult['total'];
        
        // Add ORDER BY and LIMIT
        $sql .= " ORDER BY created_at DESC LIMIT {$limit} OFFSET {$offset}";
        
        $results = $this->db->fetchAll($sql, $params);
        $results = array_map([$this, 'hideFields'], $results);
        
        return [
            'data' => $results,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1
            ]
        ];
    }
    
    /**
     * Update user profile
     * @param int $userId
     * @param array $data
     * @return bool
     */
    public function updateProfile($userId, $data) {
        // Remove sensitive fields that shouldn't be updated via profile
        unset($data['password_hash'], $data['role'], $data['is_active']);
        
        // Validate email if provided
        if (isset($data['email'])) {
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new InvalidArgumentException("Invalid email format");
            }
            
            if ($this->emailExists($data['email'], $userId)) {
                throw new InvalidArgumentException("Email already exists");
            }
        }
        
        // Validate username if provided
        if (isset($data['username'])) {
            if ($this->usernameExists($data['username'], $userId)) {
                throw new InvalidArgumentException("Username already exists");
            }
        }
        
        return $this->update($userId, $data);
    }
    
    /**
     * Deactivate user
     * @param int $userId
     * @return bool
     */
    public function deactivateUser($userId) {
        return $this->update($userId, ['is_active' => 0]);
    }
    
    /**
     * Activate user
     * @param int $userId
     * @return bool
     */
    public function activateUser($userId) {
        return $this->update($userId, ['is_active' => 1]);
    }
}

<?php
/**
 * FileUpload Model
 * MTC Invoice Management System
 */

class FileUpload extends BaseModel {
    protected $table = 'file_uploads';
    protected $fillable = [
        'invoice_data_id', 'original_filename', 'stored_filename', 
        'file_path', 'file_size', 'mime_type', 'uploaded_by', 'is_active'
    ];
    
    /**
     * Get files by invoice data ID
     * @param int $invoiceDataId
     * @return array
     */
    public function getByInvoiceData($invoiceDataId) {
        $sql = "SELECT * FROM {$this->table} WHERE invoice_data_id = ? AND is_active = 1 ORDER BY upload_timestamp DESC";
        return $this->db->fetchAll($sql, [$invoiceDataId]);
    }
    
    /**
     * Get files with user information
     * @param array $conditions
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getFilesWithUser($conditions = [], $limit = 25, $offset = 0) {
        $sql = "SELECT f.*, u.username, u.full_name 
                FROM {$this->table} f 
                LEFT JOIN users u ON f.uploaded_by = u.id 
                WHERE f.is_active = 1";
        
        $params = [];
        
        if (!empty($conditions)) {
            foreach ($conditions as $field => $value) {
                $sql .= " AND f.{$field} = ?";
                $params[] = $value;
            }
        }
        
        $sql .= " ORDER BY f.upload_timestamp DESC LIMIT {$limit} OFFSET {$offset}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get file statistics
     * @return array
     */
    public function getFileStats() {
        $stats = [];
        
        // Total files
        $stats['total_files'] = $this->count(['is_active' => 1]);
        
        // Total size
        $result = $this->db->fetchOne("SELECT SUM(file_size) as total_size FROM {$this->table} WHERE is_active = 1");
        $stats['total_size'] = (int) $result['total_size'] ?? 0;
        
        // Files by type
        $typeResult = $this->db->fetchAll("
            SELECT mime_type, COUNT(*) as count, SUM(file_size) as size 
            FROM {$this->table} 
            WHERE is_active = 1 
            GROUP BY mime_type
        ");
        
        $stats['by_type'] = [];
        foreach ($typeResult as $row) {
            $stats['by_type'][$row['mime_type']] = [
                'count' => (int) $row['count'],
                'size' => (int) $row['size']
            ];
        }
        
        // Recent uploads (last 30 days)
        $thirtyDaysAgo = date('Y-m-d H:i:s', time() - (30 * 24 * 60 * 60));
        $stats['recent_uploads'] = $this->count([
            'is_active' => 1,
            'upload_timestamp >=' => $thirtyDaysAgo
        ]);
        
        return $stats;
    }
    
    /**
     * Clean up orphaned files (files without invoice data)
     * @return int Number of files cleaned up
     */
    public function cleanupOrphanedFiles() {
        $sql = "SELECT f.* FROM {$this->table} f 
                LEFT JOIN invoice_data i ON f.invoice_data_id = i.id 
                WHERE f.invoice_data_id IS NOT NULL AND i.id IS NULL AND f.is_active = 1";
        
        $orphanedFiles = $this->db->fetchAll($sql);
        $cleanedUp = 0;
        
        foreach ($orphanedFiles as $file) {
            // Mark as inactive
            if ($this->update($file['id'], ['is_active' => 0])) {
                $cleanedUp++;
                
                // Optionally delete physical file
                if (file_exists($file['file_path'])) {
                    unlink($file['file_path']);
                }
            }
        }
        
        return $cleanedUp;
    }
    
    /**
     * Get files by date range
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getFilesByDateRange($startDate, $endDate) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE is_active = 1 
                AND upload_timestamp BETWEEN ? AND ? 
                ORDER BY upload_timestamp DESC";
        
        return $this->db->fetchAll($sql, [$startDate, $endDate]);
    }
    
    /**
     * Search files by filename
     * @param string $query
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function searchFiles($query, $limit = 25, $offset = 0) {
        $sql = "SELECT * FROM {$this->table} 
                WHERE is_active = 1 
                AND (original_filename LIKE ? OR stored_filename LIKE ?) 
                ORDER BY upload_timestamp DESC 
                LIMIT {$limit} OFFSET {$offset}";
        
        $searchTerm = "%{$query}%";
        return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
    }
    
    /**
     * Get disk usage by directory
     * @return array
     */
    public function getDiskUsage() {
        $uploadDir = UPLOAD_DIR;
        $totalSize = 0;
        $fileCount = 0;
        
        if (is_dir($uploadDir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($uploadDir, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $totalSize += $file->getSize();
                    $fileCount++;
                }
            }
        }
        
        return [
            'total_size' => $totalSize,
            'file_count' => $fileCount,
            'formatted_size' => $this->formatFileSize($totalSize)
        ];
    }
    
    /**
     * Validate file before upload
     * @param array $file
     * @return bool
     */
    public function validateFile($file) {
        // Check if file was uploaded
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            throw new Exception('Invalid file upload');
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error: ' . $file['error']);
        }
        
        // Check file size
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File size exceeds maximum allowed size');
        }
        
        // Check file type
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ALLOWED_FILE_TYPES)) {
            throw new Exception('File type not allowed');
        }
        
        // Additional security checks
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimeTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/gif'
        ];
        
        if (!in_array($mimeType, $allowedMimeTypes)) {
            throw new Exception('Invalid file type detected');
        }
        
        return true;
    }
    
    /**
     * Format file size
     * @param int $bytes
     * @return string
     */
    private function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

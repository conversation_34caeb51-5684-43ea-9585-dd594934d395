<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    try {
        // Direct database connection
        $host = 'localhost';
        $dbname = 'invoice_masudvi';
        $db_username = 'root';
        $db_password = '';
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $db_username, $db_password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo '<div class="alert alert-info">Database connection successful!</div>';
        
        // Check user
        $sql = "SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo '<div class="alert alert-success">User found: ' . htmlspecialchars($user['username']) . '</div>';
            echo '<div class="alert alert-info">Stored hash: ' . substr($user['password_hash'], 0, 20) . '...</div>';
            
            // Test password
            if (password_verify($password, $user['password_hash'])) {
                echo '<div class="alert alert-success">✅ Password verification successful!</div>';
                
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();
                
                echo '<div class="alert alert-success">Session created successfully!</div>';
                echo '<div class="alert alert-info">You can now access: <a href="dashboard.php">Dashboard</a></div>';
                
            } else {
                echo '<div class="alert alert-danger">❌ Password verification failed!</div>';
                
                // Test with plain text (in case hash is wrong)
                if ($password === 'admin123') {
                    echo '<div class="alert alert-warning">Plain text password matches. Hash might be incorrect.</div>';
                    
                    // Create new hash
                    $newHash = password_hash($password, PASSWORD_DEFAULT);
                    echo '<div class="alert alert-info">New hash would be: ' . substr($newHash, 0, 20) . '...</div>';
                    
                    // Update password hash
                    $updateSql = "UPDATE users SET password_hash = ? WHERE id = ?";
                    $updateStmt = $pdo->prepare($updateSql);
                    $updateStmt->execute([$newHash, $user['id']]);
                    
                    echo '<div class="alert alert-success">Password hash updated! Try logging in again.</div>';
                }
            }
        } else {
            echo '<div class="alert alert-danger">User not found!</div>';
            
            // Show all users
            $allUsers = $pdo->query("SELECT username, email, role, is_active FROM users")->fetchAll(PDO::FETCH_ASSOC);
            echo '<div class="alert alert-info">Available users:</div>';
            echo '<ul>';
            foreach ($allUsers as $u) {
                echo '<li>' . htmlspecialchars($u['username']) . ' (' . htmlspecialchars($u['email']) . ') - ' . $u['role'] . ' - Active: ' . ($u['is_active'] ? 'Yes' : 'No') . '</li>';
            }
            echo '</ul>';
        }
        
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
} else {
    echo '<div class="alert alert-warning">POST request required</div>';
}
?>

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.invoicegenarator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:7:5-9:40
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:8:9-66
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Need this for API 33 -->
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:5-80
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:22-77
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:5-76
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:22-73
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\49156148c3d3ce629ca1a50fd6c8b45d\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
18
19    <permission
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
20        android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-64:19
26        android:allowBackup="true"
26-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:14:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c1edbebcd026b7dc912fe9072250b2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:15:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:16:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:17:9-43
33        android:label="@string/app_name"
33-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:18:9-41
34        android:networkSecurityConfig="@xml/network_security_config"
34-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:19:9-69
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:20:9-54
36        android:supportsRtl="true"
36-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:21:9-35
37        android:theme="@style/Theme.NewInvoice" >
37-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:22:9-48
38        <activity
38-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:24:9-26:40
39            android:name="com.official.invoicegenarator.InvoiceTwo"
39-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:25:13-39
40            android:exported="false" />
40-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:26:13-37
41        <activity
41-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:27:9-29:40
42            android:name="com.official.invoicegenarator.InvoiceTraker"
42-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:28:13-42
43            android:exported="false" />
43-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:29:13-37
44        <activity
44-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:30:9-32:40
45            android:name="com.official.invoicegenarator.PdfViewerActivity"
45-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:31:13-46
46            android:exported="false" />
46-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:32:13-37
47        <activity
47-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:33:9-35:40
48            android:name="com.official.invoicegenarator.DownloadListActivity"
48-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:34:13-49
49            android:exported="false" />
49-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:35:13-37
50        <activity
50-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:36:9-38:40
51            android:name="com.official.invoicegenarator.MoneyBagActivity"
51-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:37:13-45
52            android:exported="false" />
52-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:38:13-37
53        <activity
53-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:39:9-41:40
54            android:name="com.official.invoicegenarator.FingerprintSettingsActivity"
54-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:40:13-56
55            android:exported="false" />
55-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:41:13-37
56        <activity
56-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:42:9-44:40
57            android:name="com.official.invoicegenarator.VarifyActivity"
57-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:43:13-43
58            android:exported="false" />
58-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:44:13-37
59        <activity
59-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:45:9-47:40
60            android:name="com.official.invoicegenarator.WorkerAttendenceActivity"
60-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:46:13-53
61            android:exported="false" />
61-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:47:13-37
62        <activity
62-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:48:9-50:40
63            android:name="com.official.invoicegenarator.SelectionActivity"
63-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:49:13-46
64            android:exported="false" />
64-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:50:13-37
65        <activity
65-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:51:9-54:55
66            android:name="com.official.invoicegenarator.Home"
66-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:52:13-33
67            android:exported="false"
67-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:53:13-37
68            android:windowSoftInputMode="adjustPan" />
68-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:54:13-52
69        <activity
69-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:55:9-63:20
70            android:name="com.official.invoicegenarator.MainActivity"
70-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:56:13-41
71            android:exported="true" >
71-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:57:13-36
72            <intent-filter>
72-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:58:13-62:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:17-69
73-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:17-77
75-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:27-74
76            </intent-filter>
77        </activity>
78        <activity
78-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
79            android:name="com.karumi.dexter.DexterActivity"
79-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
80            android:theme="@style/Dexter.Internal.Theme.Transparent" />
80-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a7ba6726ae1b3d7709726253fc772e5\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
81
82        <provider
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
83            android:name="androidx.startup.InitializationProvider"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
84            android:authorities="com.official.invoicegenarator.androidx-startup"
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
85            android:exported="false" >
85-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
86            <meta-data
86-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.emoji2.text.EmojiCompatInitializer"
87-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
88                android:value="androidx.startup" />
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5912c5fc90aaa6af2a64a1377c6c3730\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75a2c45962880d9b8625e5dbb93048a3\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
92            <meta-data
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
94                android:value="androidx.startup" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
95        </provider>
96
97        <receiver
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
98            android:name="androidx.profileinstaller.ProfileInstallReceiver"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
99            android:directBootAware="false"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
100            android:enabled="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
101            android:exported="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
102            android:permission="android.permission.DUMP" >
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
104                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
107                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
110                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
113                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27083e69fa3c976e4a868dfb62001e4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
114            </intent-filter>
115        </receiver>
116    </application>
117
118</manifest>

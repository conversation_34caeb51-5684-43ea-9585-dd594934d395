<?php
/**
 * API Router
 * MTC Invoice Management System
 */

require_once '../config/config.php';

// Set CORS headers for API access
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-CSRF-Token');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Parse the request URI
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = dirname($scriptName);

// Remove base path from request URI
$path = str_replace($basePath, '', $requestUri);
$path = trim($path, '/');

// Remove query string
$path = strtok($path, '?');

// Split path into segments
$segments = explode('/', $path);

// Remove 'api' from segments if present
if (isset($segments[0]) && $segments[0] === 'api') {
    array_shift($segments);
}

// Get controller and action
$controller = $segments[0] ?? 'invoice';
$action = $segments[1] ?? 'index';
$id = $segments[2] ?? null;

// Route mapping
$routes = [
    'invoice' => [
        'controller' => 'InvoiceController',
        'actions' => [
            'index' => 'index',
            'show' => 'show',
            'create' => 'create',
            'update' => 'update',
            'delete' => 'delete',
            'stats' => 'stats',
            'search' => 'search',
            'export' => 'export'
        ]
    ],
    'auth' => [
        'controller' => 'AuthController',
        'actions' => [
            'login' => 'login',
            'logout' => 'logout',
            'me' => 'me',
            'change-password' => 'changePassword',
            'update-profile' => 'updateProfile',
            'check-session' => 'checkSession'
        ]
    ],
    'users' => [
        'controller' => 'UserController',
        'actions' => [
            'index' => 'index',
            'show' => 'show',
            'create' => 'create',
            'update' => 'update',
            'delete' => 'delete'
        ]
    ],
    'files' => [
        'controller' => 'FileController',
        'actions' => [
            'upload' => 'upload',
            'download' => 'download',
            'delete' => 'delete',
            'list' => 'index'
        ]
    ]
];

try {
    // Check if route exists
    if (!isset($routes[$controller])) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Controller not found']);
        exit;
    }
    
    $route = $routes[$controller];
    
    // Check if action exists
    if (!isset($route['actions'][$action])) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Action not found']);
        exit;
    }
    
    $controllerClass = $route['controller'];
    $methodName = $route['actions'][$action];
    
    // Check if controller class exists
    if (!class_exists($controllerClass)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Controller class not found']);
        exit;
    }
    
    // Create controller instance
    $controllerInstance = new $controllerClass();
    
    // Check if method exists
    if (!method_exists($controllerInstance, $methodName)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Controller method not found']);
        exit;
    }
    
    // Add ID to request data if present
    if ($id) {
        $_GET['id'] = $id;
        $_POST['id'] = $id;
    }
    
    // Call the controller method
    $controllerInstance->$methodName();
    
} catch (Exception $e) {
    error_log("API Router error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

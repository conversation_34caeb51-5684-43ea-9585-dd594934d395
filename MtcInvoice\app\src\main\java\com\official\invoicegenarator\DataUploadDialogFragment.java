package com.official.invoicegenarator;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Toast;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.official.invoicegenarator.api.InvoiceApiService;
import java.util.function.Consumer;

public class DataUploadDialogFragment extends BottomSheetDialogFragment {

    static class DataItem {
        private String description;
        private String location;
        private String qr;
        private String lpo;
        private String inb;
        private String amount;
        private String w_a;
        private String paymentStatus;
        private long timestamp;

        public DataItem() {}

        public DataItem(String description, String location, String qr, String lpo, String inb, String amount, String w_a, String paymentStatus, long timestamp) {
            this.description = description;
            this.location = location;
            this.qr = qr;
            this.lpo = lpo;
            this.inb = inb;
            this.amount = amount;
            this.w_a = w_a;
            this.paymentStatus = paymentStatus;
            this.timestamp = timestamp;
        }

        // Getters
        public String getDescription() {
            return description;
        }

        public String getLocation() {
            return location;
        }

        public String getQr() {
            return qr;
        }

        public String getLpo() {
            return lpo;
        }

        public String getInb() {
            return inb;
        }

        public String getAmount() {
            return amount;
        }

        public String getW_a() {
            return w_a;
        }

        public String getPaymentStatus() {
            return paymentStatus;
        }

        public long getTimestamp() {
            return timestamp;
        }

        // Setters
        public void setDescription(String description) {
            this.description = description;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public void setQr(String qr) {
            this.qr = qr;
        }

        public void setLpo(String lpo) {
            this.lpo = lpo;
        }

        public void setInb(String inb) {
            this.inb = inb;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public void setW_a(String w_a) {
            this.w_a = w_a;
        }

        public void setPaymentStatus(String paymentStatus) {
            this.paymentStatus = paymentStatus;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }


    private static final String PREFS_NAME = "InvoiceTrackerPrefs";
    private EditText editTextDescription, editTextLocation, editTextQR, editTextLPO, editTextINB, editTextAmount, editTextW_A, editTextPaymentStatus;
    private ImageButton buttonUpload;
    private InvoiceApiService invoiceApiService;
    private SharedPreferences sharedPreferences;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        sharedPreferences = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fgdata_upload, container, false);

        invoiceApiService = new InvoiceApiService(getContext());

        editTextDescription = view.findViewById(R.id.editTextDescription);
        editTextLocation = view.findViewById(R.id.editTextLocation);
        editTextQR = view.findViewById(R.id.editTextQR);
        editTextLPO = view.findViewById(R.id.editTextLPO);
        editTextINB = view.findViewById(R.id.editTextINB);
        editTextAmount = view.findViewById(R.id.editTextAmount);
        editTextW_A = view.findViewById(R.id.editTextW_A);
        editTextPaymentStatus = view.findViewById(R.id.payment_status);
        buttonUpload = view.findViewById(R.id.buttonUpload);

        loadSavedData(); // Load saved data into EditText fields
        setupTextWatchers(); // Save data to SharedPreferences on text change

        buttonUpload.setOnClickListener(v -> uploadData());

        return view;
    }

    private void loadSavedData() {
        editTextDescription.setText(sharedPreferences.getString("description", ""));
        editTextLocation.setText(sharedPreferences.getString("location", ""));
        editTextQR.setText(sharedPreferences.getString("qr", ""));
        editTextLPO.setText(sharedPreferences.getString("lpo", ""));
        editTextINB.setText(sharedPreferences.getString("inb", ""));
        editTextAmount.setText(sharedPreferences.getString("amount", ""));
        editTextW_A.setText(sharedPreferences.getString("w_a", ""));
        editTextPaymentStatus.setText(sharedPreferences.getString("paymentStatus", ""));
    }

    private void setupTextWatchers() {
        editTextDescription.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("description", s)));
        editTextLocation.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("location", s)));
        editTextQR.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("qr", s)));
        editTextLPO.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("lpo", s)));
        editTextINB.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("inb", s)));
        editTextAmount.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("amount", s)));
        editTextW_A.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("w_a", s)));
        editTextPaymentStatus.addTextChangedListener(new SimpleTextWatcher(s -> saveToPrefs("paymentStatus", s)));
    }

    private void saveToPrefs(String key, String value) {
        sharedPreferences.edit().putString(key, value).apply();
    }

    private void uploadData() {
        String description = editTextDescription.getText().toString().trim();
        String location = editTextLocation.getText().toString().trim();
        String qr = editTextQR.getText().toString().trim();
        String lpo = editTextLPO.getText().toString().trim();
        String inb = editTextINB.getText().toString().trim();
        String amount = editTextAmount.getText().toString().trim();
        String w_a = editTextW_A.getText().toString().trim();
        String paymentStatus = editTextPaymentStatus.getText().toString().trim();

        if (!description.isEmpty() && !location.isEmpty() && !qr.isEmpty() && !lpo.isEmpty() && !inb.isEmpty() && !amount.isEmpty() && !w_a.isEmpty() && !paymentStatus.isEmpty()) {

            // Show loading state
            buttonUpload.setEnabled(false);

            invoiceApiService.createInvoice(description, location, qr, lpo, inb, amount, w_a, paymentStatus,
                new InvoiceApiService.InvoiceCallback() {
                    @Override
                    public void onSuccess(String message) {
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "Data Uploaded Successfully", Toast.LENGTH_SHORT).show();
                                clearFields();
                                clearSavedData(); // Clear SharedPreferences
                                buttonUpload.setEnabled(true);
                                dismiss(); // Close the dialog
                            });
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "Data Upload Failed: " + error, Toast.LENGTH_SHORT).show();
                                buttonUpload.setEnabled(true);
                            });
                        }
                    }
                });
        } else {
            Toast.makeText(getContext(), "Please fill all fields", Toast.LENGTH_SHORT).show();
        }
    }

    private void clearFields() {
        editTextDescription.setText("");
        editTextLocation.setText("");
        editTextQR.setText("");
        editTextLPO.setText("");
        editTextINB.setText("");
        editTextAmount.setText("");
        editTextW_A.setText("");
        editTextPaymentStatus.setText("");
    }

    private void clearSavedData() {
        sharedPreferences.edit().clear().apply();
    }

    private static class SimpleTextWatcher implements TextWatcher {
        private final Consumer<String> callback;

        SimpleTextWatcher(Consumer<String> callback) {
            this.callback = callback;
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                callback.accept(s.toString());
            }
        }

        @Override
        public void afterTextChanged(Editable s) {}
    }
}

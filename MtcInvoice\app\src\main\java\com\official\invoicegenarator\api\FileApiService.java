package com.official.invoicegenarator.api;

import android.content.Context;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

/**
 * File API Service
 * MTC Invoice Management System
 */
public class FileApiService {
    
    private static final String TAG = "FileApiService";
    private HttpClient httpClient;
    
    public FileApiService(Context context) {
        this.httpClient = HttpClient.getInstance(context);
    }
    
    /**
     * Upload file
     */
    public void uploadFile(File file, Integer invoiceDataId, FileUploadCallback callback) {
        // Validate file
        if (!file.exists()) {
            callback.onError("File does not exist");
            return;
        }
        
        if (!ApiConfig.isFileTypeAllowed(file.getName())) {
            callback.onError("File type not allowed. Allowed types: " + 
                           String.join(", ", ApiConfig.ALLOWED_FILE_TYPES));
            return;
        }
        
        if (!ApiConfig.isFileSizeValid(file.length())) {
            callback.onError("File size exceeds maximum limit of " + 
                           (ApiConfig.MAX_FILE_SIZE / (1024 * 1024)) + "MB");
            return;
        }
        
        try {
            JSONObject additionalData = new JSONObject();
            if (invoiceDataId != null) {
                additionalData.put("invoice_data_id", invoiceDataId);
            }
            
            httpClient.uploadFile(ApiConfig.FILE_UPLOAD, file, additionalData, new HttpClient.ApiCallback() {
                @Override
                public void onSuccess(JSONObject response) {
                    try {
                        if (response.getBoolean("success")) {
                            JSONObject data = response.getJSONObject("data");
                            callback.onSuccess(
                                data.getInt("file_id"),
                                data.getString("filename"),
                                response.getString("message")
                            );
                        } else {
                            callback.onError(response.getString("message"));
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing upload response", e);
                        callback.onError("Invalid response format");
                    }
                }
                
                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
            
        } catch (JSONException e) {
            Log.e(TAG, "Error preparing upload data", e);
            callback.onError("Failed to prepare upload data");
        }
    }
    
    /**
     * Download file
     */
    public void downloadFile(int fileId, FileDownloadCallback callback) {
        String endpoint = ApiConfig.FILE_DOWNLOAD + "?id=" + fileId;
        
        httpClient.get(endpoint, new HttpClient.ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                // For file downloads, we might need to handle binary data differently
                // This is a simplified version - in practice, you might need to handle
                // the download differently to get the actual file content
                callback.onSuccess("File download initiated");
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Delete file
     */
    public void deleteFile(int fileId, FileCallback callback) {
        try {
            JSONObject data = new JSONObject();
            data.put("id", fileId);
            
            httpClient.post(ApiConfig.FILE_DELETE, data, new HttpClient.ApiCallback() {
                @Override
                public void onSuccess(JSONObject response) {
                    try {
                        if (response.getBoolean("success")) {
                            callback.onSuccess(response.getString("message"));
                        } else {
                            callback.onError(response.getString("message"));
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing delete response", e);
                        callback.onError("Invalid response format");
                    }
                }
                
                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
            
        } catch (JSONException e) {
            Log.e(TAG, "Error deleting file", e);
            callback.onError("Failed to prepare delete request");
        }
    }
    
    /**
     * Get file list
     */
    public void getFileList(Integer invoiceDataId, FileListCallback callback) {
        String endpoint = ApiConfig.FILE_LIST;
        if (invoiceDataId != null) {
            endpoint += "?invoice_data_id=" + invoiceDataId;
        }
        
        httpClient.get(endpoint, new HttpClient.ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                try {
                    if (response.getBoolean("success")) {
                        callback.onSuccess(response.getJSONObject("data"));
                    } else {
                        callback.onError(response.getString("message"));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing file list response", e);
                    callback.onError("Invalid response format");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Get file info
     */
    public void getFileInfo(int fileId, FileInfoCallback callback) {
        String endpoint = ApiConfig.FILE_LIST + "/" + fileId;
        
        httpClient.get(endpoint, new HttpClient.ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                try {
                    if (response.getBoolean("success")) {
                        callback.onSuccess(response.getJSONObject("data"));
                    } else {
                        callback.onError(response.getString("message"));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing file info response", e);
                    callback.onError("Invalid response format");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Callback interfaces
     */
    public interface FileUploadCallback {
        void onSuccess(int fileId, String filename, String message);
        void onError(String error);
    }
    
    public interface FileDownloadCallback {
        void onSuccess(String message);
        void onError(String error);
    }
    
    public interface FileCallback {
        void onSuccess(String message);
        void onError(String error);
    }
    
    public interface FileListCallback {
        void onSuccess(JSONObject data);
        void onError(String error);
    }
    
    public interface FileInfoCallback {
        void onSuccess(JSONObject fileInfo);
        void onError(String error);
    }
}

<?php
/**
 * Base Controller Class
 * MTC Invoice Management System
 */

abstract class BaseController {
    protected $request;
    protected $response;
    protected $user;
    
    public function __construct() {
        $this->request = $this->parseRequest();
        $this->response = ['success' => false, 'message' => '', 'data' => null];
        $this->user = $this->getCurrentUser();
    }
    
    /**
     * Parse incoming request
     * @return array
     */
    protected function parseRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $input = [];
        
        switch ($method) {
            case 'GET':
                $input = $_GET;
                break;
            case 'POST':
            case 'PUT':
            case 'PATCH':
                $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
                if (strpos($contentType, 'application/json') !== false) {
                    $input = json_decode(file_get_contents('php://input'), true) ?? [];
                } else {
                    $input = $_POST;
                }
                break;
            case 'DELETE':
                parse_str(file_get_contents('php://input'), $input);
                break;
        }
        
        return [
            'method' => $method,
            'data' => sanitizeInput($input),
            'files' => $_FILES ?? [],
            'headers' => getallheaders()
        ];
    }
    
    /**
     * Get current authenticated user
     * @return array|null
     */
    protected function getCurrentUser() {
        if (isset($_SESSION['user_id'])) {
            $userModel = new User();
            return $userModel->find($_SESSION['user_id']);
        }
        return null;
    }
    
    /**
     * Check if user is authenticated
     * @return bool
     */
    protected function isAuthenticated() {
        return $this->user !== null;
    }
    
    /**
     * Check if user has required role
     * @param string|array $roles
     * @return bool
     */
    protected function hasRole($roles) {
        if (!$this->isAuthenticated()) {
            return false;
        }
        
        if (is_string($roles)) {
            $roles = [$roles];
        }
        
        return in_array($this->user['role'], $roles);
    }
    
    /**
     * Require authentication
     * @throws Exception
     */
    protected function requireAuth() {
        if (!$this->isAuthenticated()) {
            $this->sendError('Authentication required', 401);
        }
    }
    
    /**
     * Require specific role
     * @param string|array $roles
     * @throws Exception
     */
    protected function requireRole($roles) {
        $this->requireAuth();
        
        if (!$this->hasRole($roles)) {
            $this->sendError('Insufficient permissions', 403);
        }
    }
    
    /**
     * Validate CSRF token
     * @return bool
     */
    protected function validateCSRF() {
        $token = $this->request['data']['csrf_token'] ?? 
                 $this->request['headers']['X-CSRF-Token'] ?? '';
        
        return validateCSRF($token);
    }
    
    /**
     * Require CSRF validation
     * @throws Exception
     */
    protected function requireCSRF() {
        if (!$this->validateCSRF()) {
            $this->sendError('Invalid CSRF token', 400);
        }
    }
    
    /**
     * Validate required fields
     * @param array $fields
     * @param array $data
     * @throws Exception
     */
    protected function validateRequired($fields, $data = null) {
        if ($data === null) {
            $data = $this->request['data'];
        }
        
        $missing = [];
        foreach ($fields as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            $this->sendError('Missing required fields: ' . implode(', ', $missing), 400);
        }
    }
    
    /**
     * Get pagination parameters
     * @return array
     */
    protected function getPagination() {
        $page = max(1, (int) ($this->request['data']['page'] ?? 1));
        $limit = min(MAX_PAGE_SIZE, max(1, (int) ($this->request['data']['limit'] ?? DEFAULT_PAGE_SIZE)));
        
        return ['page' => $page, 'limit' => $limit];
    }
    
    /**
     * Send JSON response
     * @param array $data
     * @param int $statusCode
     */
    protected function sendResponse($data = null, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        
        $this->response['success'] = true;
        if ($data !== null) {
            $this->response['data'] = $data;
        }
        
        echo json_encode($this->response);
        exit;
    }
    
    /**
     * Send error response
     * @param string $message
     * @param int $statusCode
     * @param array $errors
     */
    protected function sendError($message, $statusCode = 400, $errors = []) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        
        $this->response['success'] = false;
        $this->response['message'] = $message;
        
        if (!empty($errors)) {
            $this->response['errors'] = $errors;
        }
        
        echo json_encode($this->response);
        exit;
    }
    
    /**
     * Send success response
     * @param string $message
     * @param array $data
     * @param int $statusCode
     */
    protected function sendSuccess($message, $data = null, $statusCode = 200) {
        $this->response['message'] = $message;
        $this->sendResponse($data, $statusCode);
    }
    
    /**
     * Log user activity
     * @param string $action
     * @param string $tableName
     * @param int $recordId
     * @param array $oldValues
     * @param array $newValues
     */
    protected function logActivity($action, $tableName, $recordId = null, $oldValues = null, $newValues = null) {
        $userId = $this->user['id'] ?? null;
        logActivity($userId, $action, $tableName, $recordId, $oldValues, $newValues);
    }
    
    /**
     * Handle file upload
     * @param array $file
     * @param array $allowedTypes
     * @param int $maxSize
     * @return array
     */
    protected function handleFileUpload($file, $allowedTypes = null, $maxSize = null) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            throw new Exception('No file uploaded or invalid file');
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error: ' . $file['error']);
        }
        
        $allowedTypes = $allowedTypes ?? ALLOWED_FILE_TYPES;
        $maxSize = $maxSize ?? MAX_FILE_SIZE;
        
        // Check file size
        if ($file['size'] > $maxSize) {
            throw new Exception('File size exceeds maximum allowed size');
        }
        
        // Check file type
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $allowedTypes)) {
            throw new Exception('File type not allowed');
        }
        
        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $fileExtension;
        $uploadPath = UPLOAD_DIR . $filename;
        
        // Create upload directory if it doesn't exist
        if (!is_dir(UPLOAD_DIR)) {
            mkdir(UPLOAD_DIR, 0755, true);
        }
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
            throw new Exception('Failed to move uploaded file');
        }
        
        return [
            'original_name' => $file['name'],
            'stored_name' => $filename,
            'path' => $uploadPath,
            'size' => $file['size'],
            'type' => $file['type']
        ];
    }
}

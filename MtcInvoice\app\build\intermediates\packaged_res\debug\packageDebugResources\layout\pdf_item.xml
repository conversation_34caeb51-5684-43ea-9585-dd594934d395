<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="5dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/gray"
    app:strokeWidth="1dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Main content container -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#353b48"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/pdfName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="PDF Name"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/pdfTimestamp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="8dp"
                    android:layout_weight="1"
                    android:text="Time and date"
                    android:textColor="@color/white"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/pdfSize"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="4dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="File Size"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="3">

                <ImageView
                    android:id="@+id/viewButton"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:src="@drawable/view" />

                <ImageView
                    android:id="@+id/deleteButton"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:src="@drawable/delete" />

                <ImageView
                    android:id="@+id/downloadButton"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_weight="1"
                    android:src="@drawable/download" />
            </LinearLayout>
        </LinearLayout>

    </RelativeLayout>
</com.google.android.material.card.MaterialCardView>

<?php
/**
 * Base Model Class
 * MTC Invoice Management System
 */

abstract class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Find record by ID
     * @param int $id
     * @return array|false
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = ?";
        $result = $this->db->fetchOne($sql, [$id]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return false;
    }
    
    /**
     * Get all records with optional conditions
     * @param array $conditions
     * @param string $orderBy
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getAll($conditions = [], $orderBy = null, $limit = null, $offset = 0) {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                if (is_array($value)) {
                    $placeholders = str_repeat('?,', count($value) - 1) . '?';
                    $whereClause[] = "{$field} IN ({$placeholders})";
                    $params = array_merge($params, $value);
                } else {
                    $whereClause[] = "{$field} = ?";
                    $params[] = $value;
                }
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $results = $this->db->fetchAll($sql, $params);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * Create new record
     * @param array $data
     * @return int|false
     */
    public function create($data) {
        $data = $this->filterFillable($data);
        
        if (empty($data)) {
            return false;
        }
        
        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ({$placeholders})";
        
        try {
            $this->db->execute($sql, array_values($data));
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Create failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update record by ID
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data) {
        $data = $this->filterFillable($data);
        
        if (empty($data)) {
            return false;
        }
        
        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = ?";
        $params = array_merge(array_values($data), [$id]);
        
        try {
            $stmt = $this->db->execute($sql, $params);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Update failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete record by ID
     * @param int $id
     * @return bool
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = ?";
        
        try {
            $stmt = $this->db->execute($sql, [$id]);
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Delete failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Count records with optional conditions
     * @param array $conditions
     * @return int
     */
    public function count($conditions = []) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach ($conditions as $field => $value) {
                $whereClause[] = "{$field} = ?";
                $params[] = $value;
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $result = $this->db->fetchOne($sql, $params);
        return (int) $result['count'];
    }
    
    /**
     * Search records
     * @param string $query
     * @param array $searchFields
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function search($query, $searchFields = [], $limit = 25, $offset = 0) {
        if (empty($searchFields)) {
            return [];
        }
        
        $whereClause = [];
        $params = [];
        
        foreach ($searchFields as $field) {
            $whereClause[] = "{$field} LIKE ?";
            $params[] = "%{$query}%";
        }
        
        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' OR ', $whereClause);
        $sql .= " LIMIT {$limit} OFFSET {$offset}";
        
        $results = $this->db->fetchAll($sql, $params);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * Filter data to only include fillable fields
     * @param array $data
     * @return array
     */
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    /**
     * Hide sensitive fields from result
     * @param array $data
     * @return array
     */
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }
        
        foreach ($this->hidden as $field) {
            unset($data[$field]);
        }
        
        return $data;
    }
    
    /**
     * Begin database transaction
     */
    public function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * Commit database transaction
     */
    public function commit() {
        return $this->db->commit();
    }
    
    /**
     * Rollback database transaction
     */
    public function rollback() {
        return $this->db->rollback();
    }
}

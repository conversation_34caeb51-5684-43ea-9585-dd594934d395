<?php
// Debug login issues
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Login Debug Information</h2>";

// 1. Check database connection
echo "<h3>1. Database Connection Test</h3>";
try {
    $host = 'localhost';
    $dbname = 'invoice_masudvi';
    $username = 'root';
    $password = '';
    
    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful<br>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Users table exists<br>";
        
        // Check users in table
        $stmt = $pdo->query("SELECT id, username, email, role, is_active FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Users in database:<br>";
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Role: {$user['role']}, Active: " . ($user['is_active'] ? 'Yes' : 'No') . "<br>";
        }
    } else {
        echo "❌ Users table does not exist<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// 2. Test password verification
echo "<h3>2. Password Verification Test</h3>";
try {
    $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE username = 'admin'");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        $storedHash = $result['password_hash'];
        echo "Stored hash: " . substr($storedHash, 0, 30) . "...<br>";
        
        // Test common passwords
        $testPasswords = ['admin123', 'password', 'admin', 'secret'];
        foreach ($testPasswords as $testPass) {
            $isValid = password_verify($testPass, $storedHash);
            echo "Testing '{$testPass}': " . ($isValid ? "✅ MATCH" : "❌ NO MATCH") . "<br>";
        }
    } else {
        echo "❌ No admin user found<br>";
    }
} catch (Exception $e) {
    echo "❌ Password test error: " . $e->getMessage() . "<br>";
}

// 3. Check API endpoint
echo "<h3>3. API Endpoint Test</h3>";
$apiUrl = 'http://192.168.0.106/MtcInvoiceMasudvi/mtcadminpanel/api/auth/login';
echo "API URL: {$apiUrl}<br>";

// 4. Check CSRF token
echo "<h3>4. CSRF Token</h3>";
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
echo "CSRF Token: " . $_SESSION['csrf_token'] . "<br>";

// 5. Test manual login
echo "<h3>5. Manual Login Test</h3>";
if ($_POST) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "Attempting login with: {$username} / {$password}<br>";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "✅ User found: " . $user['username'] . "<br>";
            
            if (password_verify($password, $user['password_hash'])) {
                echo "✅ Password verified successfully!<br>";
                echo "✅ Login should work!<br>";
            } else {
                echo "❌ Password verification failed<br>";
            }
        } else {
            echo "❌ User not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Login test error: " . $e->getMessage() . "<br>";
    }
}
?>

<form method="POST">
    <h4>Test Login</h4>
    <input type="text" name="username" placeholder="Username" value="admin" required><br><br>
    <input type="password" name="password" placeholder="Password" value="admin123" required><br><br>
    <button type="submit">Test Login</button>
</form>

<hr>
<h3>6. Create New Admin User</h3>
<?php
if (isset($_GET['create_admin'])) {
    try {
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Delete existing admin and create new one
        $pdo->exec("DELETE FROM users WHERE username = 'admin'");
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash, full_name, role, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $hashedPassword, 'System Administrator', 'admin', 1]);
        
        echo "✅ New admin user created successfully!<br>";
        echo "Username: admin<br>";
        echo "Password: admin123<br>";
        
    } catch (Exception $e) {
        echo "❌ Error creating admin: " . $e->getMessage() . "<br>";
    }
}
?>

<a href="?create_admin=1" onclick="return confirm('This will recreate the admin user. Continue?')">
    <button>Recreate Admin User</button>
</a>

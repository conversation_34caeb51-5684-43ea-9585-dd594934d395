<?php
/**
 * Invoice Controller
 * MTC Invoice Management System
 */

class InvoiceController extends BaseController {
    private $invoiceModel;
    
    public function __construct() {
        parent::__construct();
        $this->invoiceModel = new InvoiceData();
    }
    
    /**
     * Get all invoice data with pagination and filters
     */
    public function index() {
        try {
            $pagination = $this->getPagination();
            $filters = [
                'search' => $this->request['data']['search'] ?? '',
                'payment_status' => $this->request['data']['payment_status'] ?? '',
                'location' => $this->request['data']['location'] ?? '',
                'date_from' => $this->request['data']['date_from'] ?? '',
                'date_to' => $this->request['data']['date_to'] ?? '',
                'amount_min' => $this->request['data']['amount_min'] ?? '',
                'amount_max' => $this->request['data']['amount_max'] ?? ''
            ];
            
            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== '';
            });
            
            $result = $this->invoiceModel->getInvoiceData($filters, $pagination['page'], $pagination['limit']);
            
            // Format data for display
            foreach ($result['data'] as &$item) {
                $item['formatted_amount'] = formatCurrency($item['amount']);
                $item['formatted_date'] = formatDate($item['timestamp'] / 1000); // Convert from milliseconds
            }
            
            $this->sendSuccess('Invoice data retrieved successfully', $result);
            
        } catch (Exception $e) {
            error_log("Invoice index error: " . $e->getMessage());
            $this->sendError('Failed to retrieve invoice data');
        }
    }
    
    /**
     * Get single invoice data by ID
     */
    public function show() {
        try {
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('Invoice ID is required', 400);
            }
            
            $invoice = $this->invoiceModel->find($id);
            
            if (!$invoice) {
                $this->sendError('Invoice not found', 404);
            }
            
            // Format data
            $invoice['formatted_amount'] = formatCurrency($invoice['amount']);
            $invoice['formatted_date'] = formatDate($invoice['timestamp'] / 1000);
            
            $this->sendSuccess('Invoice data retrieved successfully', $invoice);
            
        } catch (Exception $e) {
            error_log("Invoice show error: " . $e->getMessage());
            $this->sendError('Failed to retrieve invoice data');
        }
    }
    
    /**
     * Create new invoice data
     */
    public function create() {
        try {
            $this->requireAuth();
            $this->requireCSRF();
            
            $requiredFields = ['description', 'location', 'qr', 'lpo', 'inb', 'amount', 'w_a', 'payment_status'];
            $this->validateRequired($requiredFields);
            
            $data = $this->request['data'];
            $data['created_by'] = $this->user['id'];
            
            $id = $this->invoiceModel->createInvoiceData($data);
            
            if ($id) {
                $this->logActivity('CREATE', 'invoice_data', $id, null, $data);
                $this->sendSuccess('Invoice data created successfully', ['id' => $id], 201);
            } else {
                $this->sendError('Failed to create invoice data');
            }
            
        } catch (InvalidArgumentException $e) {
            $this->sendError($e->getMessage(), 400);
        } catch (Exception $e) {
            error_log("Invoice create error: " . $e->getMessage());
            $this->sendError('Failed to create invoice data');
        }
    }
    
    /**
     * Update invoice data
     */
    public function update() {
        try {
            $this->requireAuth();
            $this->requireCSRF();
            
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('Invoice ID is required', 400);
            }
            
            // Get existing data for logging
            $oldData = $this->invoiceModel->find($id);
            if (!$oldData) {
                $this->sendError('Invoice not found', 404);
            }
            
            $data = $this->request['data'];
            unset($data['id']); // Remove ID from update data
            
            $success = $this->invoiceModel->updateInvoiceData($id, $data);
            
            if ($success) {
                $this->logActivity('UPDATE', 'invoice_data', $id, $oldData, $data);
                $this->sendSuccess('Invoice data updated successfully');
            } else {
                $this->sendError('Failed to update invoice data');
            }
            
        } catch (InvalidArgumentException $e) {
            $this->sendError($e->getMessage(), 400);
        } catch (Exception $e) {
            error_log("Invoice update error: " . $e->getMessage());
            $this->sendError('Failed to update invoice data');
        }
    }
    
    /**
     * Delete invoice data
     */
    public function delete() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin', 'manager']);
            $this->requireCSRF();
            
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('Invoice ID is required', 400);
            }
            
            // Get existing data for logging
            $oldData = $this->invoiceModel->find($id);
            if (!$oldData) {
                $this->sendError('Invoice not found', 404);
            }
            
            $success = $this->invoiceModel->delete($id);
            
            if ($success) {
                $this->logActivity('DELETE', 'invoice_data', $id, $oldData, null);
                $this->sendSuccess('Invoice data deleted successfully');
            } else {
                $this->sendError('Failed to delete invoice data');
            }
            
        } catch (Exception $e) {
            error_log("Invoice delete error: " . $e->getMessage());
            $this->sendError('Failed to delete invoice data');
        }
    }
    
    /**
     * Get dashboard statistics
     */
    public function stats() {
        try {
            $this->requireAuth();
            
            $stats = $this->invoiceModel->getDashboardStats();
            
            // Format amounts
            $stats['formatted_total_amount'] = formatCurrency($stats['total_amount']);
            
            foreach ($stats['by_status'] as $status => &$data) {
                $data['formatted_amount'] = formatCurrency($data['amount']);
            }
            
            $this->sendSuccess('Statistics retrieved successfully', $stats);
            
        } catch (Exception $e) {
            error_log("Invoice stats error: " . $e->getMessage());
            $this->sendError('Failed to retrieve statistics');
        }
    }
    
    /**
     * Search invoice data
     */
    public function search() {
        try {
            $query = $this->request['data']['q'] ?? '';
            
            if (strlen($query) < 2) {
                $this->sendError('Search query must be at least 2 characters', 400);
            }
            
            $pagination = $this->getPagination();
            $searchFields = ['description', 'location', 'qr', 'lpo', 'inb'];
            
            $results = $this->invoiceModel->search($query, $searchFields, $pagination['limit'], ($pagination['page'] - 1) * $pagination['limit']);
            
            // Format data
            foreach ($results as &$item) {
                $item['formatted_amount'] = formatCurrency($item['amount']);
                $item['formatted_date'] = formatDate($item['timestamp'] / 1000);
            }
            
            $this->sendSuccess('Search completed successfully', $results);
            
        } catch (Exception $e) {
            error_log("Invoice search error: " . $e->getMessage());
            $this->sendError('Search failed');
        }
    }
    
    /**
     * Export invoice data
     */
    public function export() {
        try {
            $this->requireAuth();
            
            $format = $this->request['data']['format'] ?? 'csv';
            $filters = [
                'payment_status' => $this->request['data']['payment_status'] ?? '',
                'date_from' => $this->request['data']['date_from'] ?? '',
                'date_to' => $this->request['data']['date_to'] ?? ''
            ];
            
            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== '';
            });
            
            $result = $this->invoiceModel->getInvoiceData($filters, 1, 10000); // Large limit for export
            
            if ($format === 'csv') {
                $this->exportCSV($result['data']);
            } else {
                $this->sendError('Unsupported export format', 400);
            }
            
        } catch (Exception $e) {
            error_log("Invoice export error: " . $e->getMessage());
            $this->sendError('Export failed');
        }
    }
    
    /**
     * Export data as CSV
     * @param array $data
     */
    private function exportCSV($data) {
        $filename = 'invoice_data_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        $headers = ['ID', 'Description', 'Location', 'QR', 'LPO', 'INB', 'Amount', 'W/A', 'Payment Status', 'Date'];
        fputcsv($output, $headers);
        
        // CSV data
        foreach ($data as $row) {
            $csvRow = [
                $row['id'],
                $row['description'],
                $row['location'],
                $row['qr'],
                $row['lpo'],
                $row['inb'],
                $row['amount'],
                $row['w_a'],
                $row['payment_status'],
                formatDate($row['timestamp'] / 1000)
            ];
            fputcsv($output, $csvRow);
        }
        
        fclose($output);
        exit;
    }
}

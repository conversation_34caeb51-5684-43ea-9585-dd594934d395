<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    android:padding="2dp"
    app:cardCornerRadius="12dp"
    app:strokeColor="@color/colorAccent"
    app:strokeWidth="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/gray"
        android:orientation="vertical">

        <TextView
            android:id="@+id/exit_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            android:paddingBottom="16dp"
            android:text="Are you sure you want to close First Invoice?"
            android:textColor="@color/red"
            android:textSize="18sp"
            android:textStyle="bold" />

        <CheckBox
            android:id="@+id/checkbox_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            android:paddingBottom="16dp"
            android:text="Don't show this again"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Buttons container -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="9dp"
            android:gravity="end"
            android:orientation="horizontal">

            <Button
                android:id="@+id/button_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Cancel" />

            <Button
                android:id="@+id/button_yes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="Yes" />
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>

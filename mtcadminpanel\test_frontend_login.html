<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Login Test - MTC Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card mt-5">
                    <div class="card-header">
                        <h4 class="text-center">
                            <i class="bi bi-bug"></i> Frontend Login Test
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Test Login Form</h5>
                                <form id="testLoginForm">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" name="username" value="admin" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="password" name="password" value="admin123" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100" id="testLoginBtn">
                                        <span class="spinner-border spinner-border-sm d-none" id="testSpinner"></span>
                                        <i class="bi bi-play-circle"></i> Test Login
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h5>Test Results</h5>
                                <div id="testResults" class="border rounded p-3" style="min-height: 200px; background: #f8f9fa;">
                                    <p class="text-muted">Click "Test Login" to run the test...</p>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>System Information</h5>
                                <div id="systemInfo" class="border rounded p-3" style="background: #f8f9fa;">
                                    <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
                                    <p><strong>API Base URL:</strong> <span id="apiBaseUrl"></span></p>
                                    <p><strong>CSRF Token:</strong> <span id="csrfToken"></span></p>
                                    <p><strong>Session Storage:</strong> <span id="sessionStorage"></span></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="login.php" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left"></i> Back to Main Login
                            </a>
                            <a href="final_login_test.php" class="btn btn-outline-secondary">
                                <i class="bi bi-server"></i> Backend Test
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize system info
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemInfo();
        });
        
        function updateSystemInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('apiBaseUrl').textContent = window.location.origin + window.location.pathname.replace('test_frontend_login.html', 'api/');
            
            // Try to get CSRF token
            fetch('api/auth/me')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('csrfToken').textContent = 'Available (check network tab)';
                })
                .catch(error => {
                    document.getElementById('csrfToken').textContent = 'Error fetching token';
                });
            
            document.getElementById('sessionStorage').textContent = typeof(Storage) !== "undefined" ? 'Supported' : 'Not supported';
        }
        
        // Test login form
        document.getElementById('testLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const testBtn = document.getElementById('testLoginBtn');
            const spinner = document.getElementById('testSpinner');
            const resultsDiv = document.getElementById('testResults');
            
            // Show loading
            testBtn.disabled = true;
            spinner.classList.remove('d-none');
            
            resultsDiv.innerHTML = '<p class="text-info"><i class="bi bi-hourglass-split"></i> Testing login...</p>';
            
            // First, get CSRF token
            fetch('login.php')
                .then(response => response.text())
                .then(html => {
                    // Extract CSRF token from the page
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const scripts = doc.querySelectorAll('script');
                    let csrfToken = '';
                    
                    scripts.forEach(script => {
                        const content = script.textContent;
                        const match = content.match(/const CSRF_TOKEN = '([^']+)'/);
                        if (match) {
                            csrfToken = match[1];
                        }
                    });
                    
                    if (!csrfToken) {
                        throw new Error('CSRF token not found');
                    }
                    
                    resultsDiv.innerHTML += '<p class="text-success"><i class="bi bi-check"></i> CSRF token obtained: ' + csrfToken.substring(0, 20) + '...</p>';
                    
                    // Now attempt login
                    const loginData = {
                        username: formData.get('username'),
                        password: formData.get('password'),
                        remember_me: 0,
                        csrf_token: csrfToken
                    };
                    
                    return fetch('api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-Token': csrfToken
                        },
                        body: JSON.stringify(loginData)
                    });
                })
                .then(response => {
                    resultsDiv.innerHTML += '<p class="text-info"><i class="bi bi-info-circle"></i> API Response Status: ' + response.status + '</p>';
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        resultsDiv.innerHTML += '<p class="text-success"><i class="bi bi-check-circle"></i> <strong>Login Successful!</strong></p>';
                        resultsDiv.innerHTML += '<p class="text-success">Message: ' + (data.message || 'N/A') + '</p>';
                        if (data.data && data.data.redirect) {
                            resultsDiv.innerHTML += '<p class="text-success">Redirect URL: ' + data.data.redirect + '</p>';
                            resultsDiv.innerHTML += '<p class="text-warning"><i class="bi bi-arrow-right"></i> <a href="' + data.data.redirect + '" class="btn btn-sm btn-success">Go to Dashboard</a></p>';
                        }
                    } else {
                        resultsDiv.innerHTML += '<p class="text-danger"><i class="bi bi-x-circle"></i> <strong>Login Failed!</strong></p>';
                        resultsDiv.innerHTML += '<p class="text-danger">Error: ' + (data.message || 'Unknown error') + '</p>';
                    }
                    
                    // Show full response
                    resultsDiv.innerHTML += '<hr><h6>Full API Response:</h6>';
                    resultsDiv.innerHTML += '<pre style="font-size: 12px; background: #e9ecef; padding: 10px; border-radius: 3px;">' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    resultsDiv.innerHTML += '<p class="text-danger"><i class="bi bi-exclamation-triangle"></i> <strong>Error:</strong> ' + error.message + '</p>';
                    console.error('Login test error:', error);
                })
                .finally(() => {
                    testBtn.disabled = false;
                    spinner.classList.add('d-none');
                });
        });
    </script>
</body>
</html>

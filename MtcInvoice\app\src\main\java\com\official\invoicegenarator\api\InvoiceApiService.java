package com.official.invoicegenarator.api;

import android.content.Context;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Invoice API Service
 * MTC Invoice Management System
 */
public class InvoiceApiService {
    
    private static final String TAG = "InvoiceApiService";
    private HttpClient httpClient;
    
    public InvoiceApiService(Context context) {
        this.httpClient = HttpClient.getInstance(context);
    }
    
    /**
     * Create new invoice
     */
    public void createInvoice(String description, String location, String qr, String lpo, 
                             String inb, String amount, String wa, String paymentStatus, 
                             InvoiceCallback callback) {
        
        try {
            JSONObject data = new JSONObject();
            data.put("description", description);
            data.put("location", location);
            data.put("qr", qr);
            data.put("lpo", lpo);
            data.put("inb", inb);
            data.put("amount", amount);
            data.put("w_a", wa);
            data.put("payment_status", paymentStatus);
            data.put("timestamp", System.currentTimeMillis());
            
            httpClient.post(ApiConfig.INVOICE_CREATE, data, new HttpClient.ApiCallback() {
                @Override
                public void onSuccess(JSONObject response) {
                    try {
                        if (response.getBoolean("success")) {
                            callback.onSuccess(response.getString("message"));
                        } else {
                            callback.onError(response.getString("message"));
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing create response", e);
                        callback.onError("Invalid response format");
                    }
                }
                
                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
            
        } catch (JSONException e) {
            Log.e(TAG, "Error creating invoice data", e);
            callback.onError("Failed to prepare invoice data");
        }
    }
    
    /**
     * Update existing invoice
     */
    public void updateInvoice(int id, String description, String location, String qr, String lpo, 
                             String inb, String amount, String wa, String paymentStatus, 
                             InvoiceCallback callback) {
        
        try {
            JSONObject data = new JSONObject();
            data.put("id", id);
            data.put("description", description);
            data.put("location", location);
            data.put("qr", qr);
            data.put("lpo", lpo);
            data.put("inb", inb);
            data.put("amount", amount);
            data.put("w_a", wa);
            data.put("payment_status", paymentStatus);
            
            httpClient.post(ApiConfig.INVOICE_UPDATE, data, new HttpClient.ApiCallback() {
                @Override
                public void onSuccess(JSONObject response) {
                    try {
                        if (response.getBoolean("success")) {
                            callback.onSuccess(response.getString("message"));
                        } else {
                            callback.onError(response.getString("message"));
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing update response", e);
                        callback.onError("Invalid response format");
                    }
                }
                
                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
            
        } catch (JSONException e) {
            Log.e(TAG, "Error updating invoice data", e);
            callback.onError("Failed to prepare invoice data");
        }
    }
    
    /**
     * Delete invoice
     */
    public void deleteInvoice(int id, InvoiceCallback callback) {
        try {
            JSONObject data = new JSONObject();
            data.put("id", id);
            
            httpClient.post(ApiConfig.INVOICE_DELETE, data, new HttpClient.ApiCallback() {
                @Override
                public void onSuccess(JSONObject response) {
                    try {
                        if (response.getBoolean("success")) {
                            callback.onSuccess(response.getString("message"));
                        } else {
                            callback.onError(response.getString("message"));
                        }
                    } catch (JSONException e) {
                        Log.e(TAG, "Error parsing delete response", e);
                        callback.onError("Invalid response format");
                    }
                }
                
                @Override
                public void onError(String error) {
                    callback.onError(error);
                }
            });
            
        } catch (JSONException e) {
            Log.e(TAG, "Error deleting invoice", e);
            callback.onError("Failed to prepare delete request");
        }
    }
    
    /**
     * Get invoice list
     */
    public void getInvoiceList(int page, int limit, InvoiceListCallback callback) {
        String endpoint = ApiConfig.INVOICE_LIST + "?page=" + page + "&limit=" + limit;
        
        httpClient.get(endpoint, new HttpClient.ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                try {
                    if (response.getBoolean("success")) {
                        callback.onSuccess(response.getJSONObject("data"));
                    } else {
                        callback.onError(response.getString("message"));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing list response", e);
                    callback.onError("Invalid response format");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Search invoices
     */
    public void searchInvoices(String query, InvoiceListCallback callback) {
        String endpoint = ApiConfig.INVOICE_SEARCH + "?q=" + query;
        
        httpClient.get(endpoint, new HttpClient.ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                try {
                    if (response.getBoolean("success")) {
                        callback.onSuccess(response.getJSONArray("data"));
                    } else {
                        callback.onError(response.getString("message"));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing search response", e);
                    callback.onError("Invalid response format");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Get single invoice
     */
    public void getInvoice(int id, InvoiceDetailCallback callback) {
        String endpoint = ApiConfig.INVOICE_SHOW + "?id=" + id;
        
        httpClient.get(endpoint, new HttpClient.ApiCallback() {
            @Override
            public void onSuccess(JSONObject response) {
                try {
                    if (response.getBoolean("success")) {
                        callback.onSuccess(response.getJSONObject("data"));
                    } else {
                        callback.onError(response.getString("message"));
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing invoice response", e);
                    callback.onError("Invalid response format");
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Callback interfaces
     */
    public interface InvoiceCallback {
        void onSuccess(String message);
        void onError(String error);
    }
    
    public interface InvoiceListCallback {
        void onSuccess(Object data); // Can be JSONObject or JSONArray
        void onError(String error);
    }
    
    public interface InvoiceDetailCallback {
        void onSuccess(JSONObject invoice);
        void onError(String error);
    }
}

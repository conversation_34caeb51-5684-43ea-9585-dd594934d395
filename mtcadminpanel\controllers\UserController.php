<?php
/**
 * User Controller
 * MTC Invoice Management System
 */

class UserController extends BaseController {
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        $this->userModel = new User();
    }
    
    /**
     * Get all users with pagination and filters
     */
    public function index() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin', 'manager']);
            
            $pagination = $this->getPagination();
            $filters = [
                'search' => $this->request['data']['search'] ?? '',
                'role' => $this->request['data']['role'] ?? '',
                'is_active' => isset($this->request['data']['is_active']) ? (bool)$this->request['data']['is_active'] : null
            ];
            
            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== '' && $value !== null;
            });
            
            $result = $this->userModel->getUsers($filters, $pagination['page'], $pagination['limit']);
            
            $this->sendSuccess('Users retrieved successfully', $result);
            
        } catch (Exception $e) {
            error_log("User index error: " . $e->getMessage());
            $this->sendError('Failed to retrieve users');
        }
    }
    
    /**
     * Get single user by ID
     */
    public function show() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin', 'manager']);
            
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('User ID is required', 400);
            }
            
            $user = $this->userModel->find($id);
            
            if (!$user) {
                $this->sendError('User not found', 404);
            }
            
            $this->sendSuccess('User retrieved successfully', $user);
            
        } catch (Exception $e) {
            error_log("User show error: " . $e->getMessage());
            $this->sendError('Failed to retrieve user');
        }
    }
    
    /**
     * Create new user
     */
    public function create() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin']);
            $this->requireCSRF();
            
            $requiredFields = ['username', 'email', 'password', 'full_name'];
            $this->validateRequired($requiredFields);
            
            $data = $this->request['data'];
            
            // Validate password strength
            if (strlen($data['password']) < 6) {
                $this->sendError('Password must be at least 6 characters long', 400);
            }
            
            // Set default role if not provided
            if (empty($data['role'])) {
                $data['role'] = 'user';
            }
            
            // Validate role
            $validRoles = ['admin', 'manager', 'user'];
            if (!in_array($data['role'], $validRoles)) {
                $this->sendError('Invalid role specified', 400);
            }
            
            $id = $this->userModel->createUser($data);
            
            if ($id) {
                $this->logActivity('CREATE', 'users', $id, null, $data);
                $this->sendSuccess('User created successfully', ['id' => $id], 201);
            } else {
                $this->sendError('Failed to create user');
            }
            
        } catch (InvalidArgumentException $e) {
            $this->sendError($e->getMessage(), 400);
        } catch (Exception $e) {
            error_log("User create error: " . $e->getMessage());
            $this->sendError('Failed to create user');
        }
    }
    
    /**
     * Update user
     */
    public function update() {
        try {
            $this->requireAuth();
            $this->requireCSRF();
            
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('User ID is required', 400);
            }
            
            // Check permissions
            if ($this->user['id'] != $id) {
                $this->requireRole(['admin', 'manager']);
            }
            
            // Get existing data for logging
            $oldData = $this->userModel->find($id);
            if (!$oldData) {
                $this->sendError('User not found', 404);
            }
            
            $data = $this->request['data'];
            unset($data['id']); // Remove ID from update data
            
            // Handle password update
            if (!empty($data['password'])) {
                if (strlen($data['password']) < 6) {
                    $this->sendError('Password must be at least 6 characters long', 400);
                }
                
                // Only admin can change other users' passwords
                if ($this->user['id'] != $id && !$this->hasRole(['admin'])) {
                    $this->sendError('Insufficient permissions to change password', 403);
                }
                
                $success = $this->userModel->updatePassword($id, $data['password']);
                unset($data['password']); // Remove from regular update
                
                if (!$success) {
                    $this->sendError('Failed to update password');
                }
            }
            
            // Handle role update (admin only)
            if (isset($data['role'])) {
                if (!$this->hasRole(['admin'])) {
                    $this->sendError('Insufficient permissions to change role', 403);
                }
                
                $validRoles = ['admin', 'manager', 'user'];
                if (!in_array($data['role'], $validRoles)) {
                    $this->sendError('Invalid role specified', 400);
                }
            }
            
            // Handle status update (admin/manager only)
            if (isset($data['is_active'])) {
                if (!$this->hasRole(['admin', 'manager'])) {
                    $this->sendError('Insufficient permissions to change status', 403);
                }
                
                // Prevent self-deactivation
                if ($this->user['id'] == $id && !$data['is_active']) {
                    $this->sendError('Cannot deactivate your own account', 400);
                }
            }
            
            // Update profile data
            if (!empty($data)) {
                if ($this->user['id'] == $id) {
                    // User updating their own profile
                    $success = $this->userModel->updateProfile($id, $data);
                } else {
                    // Admin/manager updating other user
                    $success = $this->userModel->update($id, $data);
                }
                
                if (!$success) {
                    $this->sendError('Failed to update user');
                }
            }
            
            $this->logActivity('UPDATE', 'users', $id, $oldData, $data);
            $this->sendSuccess('User updated successfully');
            
        } catch (InvalidArgumentException $e) {
            $this->sendError($e->getMessage(), 400);
        } catch (Exception $e) {
            error_log("User update error: " . $e->getMessage());
            $this->sendError('Failed to update user');
        }
    }
    
    /**
     * Delete user (deactivate)
     */
    public function delete() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin']);
            $this->requireCSRF();
            
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('User ID is required', 400);
            }
            
            // Prevent self-deletion
            if ($this->user['id'] == $id) {
                $this->sendError('Cannot delete your own account', 400);
            }
            
            // Get existing data for logging
            $oldData = $this->userModel->find($id);
            if (!$oldData) {
                $this->sendError('User not found', 404);
            }
            
            // Deactivate instead of delete
            $success = $this->userModel->deactivateUser($id);
            
            if ($success) {
                $this->logActivity('DEACTIVATE', 'users', $id, $oldData, null);
                $this->sendSuccess('User deactivated successfully');
            } else {
                $this->sendError('Failed to deactivate user');
            }
            
        } catch (Exception $e) {
            error_log("User delete error: " . $e->getMessage());
            $this->sendError('Failed to deactivate user');
        }
    }
    
    /**
     * Activate user
     */
    public function activate() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin', 'manager']);
            $this->requireCSRF();
            
            $id = $this->request['data']['id'] ?? null;
            
            if (!$id) {
                $this->sendError('User ID is required', 400);
            }
            
            $success = $this->userModel->activateUser($id);
            
            if ($success) {
                $this->logActivity('ACTIVATE', 'users', $id);
                $this->sendSuccess('User activated successfully');
            } else {
                $this->sendError('Failed to activate user');
            }
            
        } catch (Exception $e) {
            error_log("User activate error: " . $e->getMessage());
            $this->sendError('Failed to activate user');
        }
    }
    
    /**
     * Get user statistics
     */
    public function stats() {
        try {
            $this->requireAuth();
            $this->requireRole(['admin', 'manager']);
            
            $stats = [
                'total_users' => $this->userModel->count(),
                'active_users' => $this->userModel->count(['is_active' => 1]),
                'inactive_users' => $this->userModel->count(['is_active' => 0]),
                'by_role' => []
            ];
            
            // Get users by role
            $roles = ['admin', 'manager', 'user'];
            foreach ($roles as $role) {
                $stats['by_role'][$role] = $this->userModel->count(['role' => $role, 'is_active' => 1]);
            }
            
            $this->sendSuccess('User statistics retrieved successfully', $stats);
            
        } catch (Exception $e) {
            error_log("User stats error: " . $e->getMessage());
            $this->sendError('Failed to retrieve user statistics');
        }
    }
}

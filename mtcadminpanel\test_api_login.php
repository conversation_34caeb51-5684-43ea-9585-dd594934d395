<?php
// Test API login flow
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>API Login Flow Test</h2>";

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$csrfToken = $_SESSION['csrf_token'];
echo "<p><strong>CSRF Token:</strong> " . substr($csrfToken, 0, 20) . "...</p>";

// Test API endpoint
$apiUrl = 'http://192.168.0.106/MtcInvoiceMasudvi/mtcadminpanel/api/auth/login';
echo "<p><strong>API URL:</strong> {$apiUrl}</p>";

// Prepare login data
$loginData = [
    'username' => 'admin',
    'password' => 'admin123',
    'csrf_token' => $csrfToken
];

echo "<h3>Testing API Login...</h3>";

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($loginData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'X-CSRF-Token: ' . $csrfToken
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h4>API Response:</h4>";
echo "<p><strong>HTTP Code:</strong> {$httpCode}</p>";

if ($error) {
    echo "<p><strong>cURL Error:</strong> {$error}</p>";
} else {
    echo "<p><strong>Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // Try to decode JSON response
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "<h4>Parsed Response:</h4>";
        echo "<ul>";
        echo "<li><strong>Success:</strong> " . ($responseData['success'] ? 'true' : 'false') . "</li>";
        echo "<li><strong>Message:</strong> " . htmlspecialchars($responseData['message'] ?? 'N/A') . "</li>";
        if (isset($responseData['data'])) {
            echo "<li><strong>Data:</strong> " . htmlspecialchars(json_encode($responseData['data'])) . "</li>";
        }
        echo "</ul>";
    }
}

// Test direct authentication (bypass API)
echo "<hr><h3>Testing Direct Authentication...</h3>";

try {
    require_once 'config/config.php';
    require_once 'models/User.php';
    
    $userModel = new User();
    $user = $userModel->authenticate('admin', 'admin123');
    
    if ($user) {
        echo "<p>✅ <strong>Direct authentication successful!</strong></p>";
        echo "<ul>";
        echo "<li>User ID: " . $user['id'] . "</li>";
        echo "<li>Username: " . $user['username'] . "</li>";
        echo "<li>Role: " . $user['role'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p>❌ <strong>Direct authentication failed!</strong></p>";
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>Direct authentication error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test CSRF validation
echo "<hr><h3>Testing CSRF Validation...</h3>";

try {
    require_once 'config/config.php';
    
    // Test the validateCSRF function
    $isValidCSRF = validateCSRF($csrfToken);
    echo "<p><strong>CSRF Validation:</strong> " . ($isValidCSRF ? "✅ Valid" : "❌ Invalid") . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>CSRF validation error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>← Back to Login</a> | <a href='simple_fix_login.php'>Try Simple Login</a></p>";
?>
